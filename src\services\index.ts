/**
 * 服务层统一导出
 * 提供所有服务的便捷访问入口，包括离线和在线服务
 */

// ==================== 离线服务 ====================

// 基础服务类型和接口
export * from './types/ServiceTypes';

// 基础服务类
export { BaseService } from './base/BaseService';
export { BaseRepository } from './base/BaseRepository';
export { DatabaseService } from './base/DatabaseService';

// 导出数据库相关类型
export type {
  DatabaseUpgradeStatement,
  IDbVersionService,
  ISQLiteService
} from './base/DatabaseService';

// 基础翻译类
export { TranslatableRepository } from './base/TranslatableRepository';
export type { TranslatableEntity, Translation } from './base/TranslatableRepository';
export { TranslatableService } from './base/TranslatableService';
export type { TranslatableCreateInput, TranslatableUpdateInput } from './base/TranslatableService';

// 实体服务 - 使用版本
// 注意：以下deprecated服务已被移除，因为对应的文件不存在
// export { MoodEntryService } from './entities/deprecated/MoodEntryService';
// export { EmotionSelectionService } from './entities/deprecated/EmotionSelectionService';
// export { EmotionService } from './entities/deprecated/EmotionService';
// export { EmotionDataSetService } from './entities/deprecated/EmotionDataSetService';
// export { EmotionDataSetTierService } from './entities/deprecated/EmotionDataSetTierService';
// export { EmojiSetService } from './entities/deprecated/EmojiSetService';
// export { EmojiItemService } from './entities/deprecated/EmojiItemService';
// export { EmotionDataSetEmotionService } from './entities/deprecated/EmotionDataSetEmotionService';

export { UserConfigService } from './entities/UserConfigService';
export { TagService } from './entities/TagService';
export { SkinService } from './entities/SkinService';
export { UILabelService } from './entities/UILabelService';

// Quiz相关服务 - 使用版本
export { QuizPackService } from './entities/QuizPackService';
export { QuizSessionService } from './entities/QuizSessionService';
export { QuizAnswerService } from './entities/QuizAnswerService';
export { QuizQuestionService } from './entities/QuizQuestionService';
export { QuizQuestionOptionService } from './entities/QuizQuestionOptionService';
// 注意：QuizEngineV2 文件不存在，使用 QuizEngineV3 替代
// export { QuizEngineV2 } from './quiz/QuizEngineV2';
export { QuizEngineV3 } from './entities/QuizEngineV3';

// 配置系统服务
export { GlobalAppConfigService } from './entities/GlobalAppConfigService';
export { UserQuizPreferencesService } from './entities/UserQuizPreferencesService';
export { QuizConfigMergerService } from './entities/QuizConfigMergerService';

// 实体仓储 - 使用版本
// 注意：以下deprecated仓储已被移除，因为对应的文件不存在
// export { MoodEntryRepository } from './entities/deprecated/MoodEntryRepository';
// export { EmotionSelectionRepository } from './entities/deprecated/EmotionSelectionRepository';
// export { EmotionRepository } from './entities/deprecated/EmotionRepository';
// export { EmotionDataSetRepository } from './entities/deprecated/EmotionDataSetRepository';
// export { EmotionDataSetTierRepository } from './entities/deprecated/EmotionDataSetTierRepository';
// export { EmojiSetRepository } from './entities/deprecated/EmojiSetRepository';
// export { EmojiItemRepository } from './entities/deprecated/EmojiItemRepository';
// export { EmotionDataSetEmotionRepository } from './entities/deprecated/EmotionDataSetEmotionRepository';

export { SkinRepository } from './entities/SkinRepository';

// Quiz相关仓储 - 使用版本
export { QuizPackRepository } from './entities/QuizPackRepository';
export { QuizQuestionRepository } from './entities/QuizQuestionRepository';
export { QuizQuestionOptionRepository } from './entities/QuizQuestionOptionRepository';
export { QuizSessionRepository } from './entities/QuizSessionRepository';
export { QuizAnswerRepository } from './entities/QuizAnswerRepository';

// 配置系统仓储
export { GlobalAppConfigRepository } from './entities/GlobalAppConfigRepository';
export { UserQuizPreferencesRepository } from './entities/UserQuizPreferencesRepository';
export { QuizPackOverridesRepository } from './entities/QuizPackOverridesRepository';
export { QuizSessionConfigRepository } from './entities/QuizSessionConfigRepository';

// 业务逻辑服务
export { MoodTrackingService } from './business/MoodTrackingService';

// ==================== 在线服务 ====================

// 在线服务导出
export * from './online';

// 在线服务别名导出（避免命名冲突）
export {
  OnlineServices,
  initializeOnlineServices,
  cleanupOnlineServices,
  // checkOnlineServicesHealth
} from './online';

// 服务类型定义
// 注意：以下deprecated服务类型已被移除，因为对应的文件不存在
// export type {
//   CreateMoodEntryInput,
//   UpdateMoodEntryInput
// } from './entities/deprecated/MoodEntryService';

// export type {
//   CreateEmotionSelectionInput,
//   UpdateEmotionSelectionInput,
//   BatchCreateEmotionSelectionsInput
// } from './entities/deprecated/EmotionSelectionService';

// export type {
//   CreateEmotionInput,
//   UpdateEmotionInput
// } from './entities/deprecated/EmotionService';

// export type {
//   CreateEmotionDataSetData,
//   UpdateEmotionDataSetData
// } from './entities/deprecated/EmotionDataSetService';

// export type {
//   CreateEmotionDataSetTierInput,
//   UpdateEmotionDataSetTierInput
// } from './entities/deprecated/EmotionDataSetTierService';

// export type {
//   CreateEmotionDataSetEmotionInput,
//   UpdateEmotionDataSetEmotionInput
// } from './entities/deprecated/EmotionDataSetEmotionService';

// 注意：以下服务类型引用的文件不存在，已注释
// export type {
//   CreateUserConfigInput,
//   UpdateUserConfigServiceInput
// } from './entities/UserConfigService';

// export type {
//   CreateTagInput,
//   UpdateTagInput
// } from './entities/TagService';

// export type {
//   CreateUILabelInput,
//   UpdateUILabelInput
// } from './entities/UILabelService';

export type {
  CreateEmojiSetInput,
  UpdateEmojiSetInput,
  CreateEmojiItemInput,
  UpdateEmojiItemInput,
  CreateSkinInput,
  UpdateSkinInput
} from '../types/schema/api';

export type {
  CompleteMoodEntryInput,
  MoodEntryWithSelections,
  MoodTrackingStats
} from './business/MoodTrackingService';

// 注意：以下Quiz服务类型引用的文件不存在，已注释
// export type {
//   CreateQuizSessionInput,
//   UpdateQuizSessionInput,
//   QuizSessionFilter,
//   QuizSessionStats
// } from './entities/QuizSessionService';

// export type {
//   CreateQuizAnswerInput,
//   UpdateQuizAnswerInput,
//   QuizAnswerFilter,
//   QuizAnswerStats
// } from './entities/QuizAnswerService';

// 仓储类型定义
// 注意：以下deprecated仓储类型已被移除，因为对应的文件不存在
// export type {
//   CreateMoodEntryData,
//   UpdateMoodEntryData,
//   MoodEntryFilter
// } from './entities/deprecated/MoodEntryRepository';

// export type {
//   CreateEmotionSelectionData,
//   UpdateEmotionSelectionData,
//   EmotionSelectionFilter
// } from './entities/deprecated/EmotionSelectionRepository';

// export type {
//   CreateUserConfigData,
//   UpdateUserConfigData,
//   UserConfigFilter
// } from './entities/UserConfigRepository';

/**
 * 服务工厂类
 * 提供服务实例的统一管理
 */
export class ServiceFactory {
  private static instance: ServiceFactory;
  private services: Map<string, any> = new Map();
  private db?: any; // SQLiteDBConnection

  private constructor(db?: any) {
    this.db = db;
  }

  static getInstance(db?: any): ServiceFactory {
    if (!ServiceFactory.instance) {
      ServiceFactory.instance = new ServiceFactory(db);
    }
    return ServiceFactory.instance;
  }

  static reset(): void {
    ServiceFactory.instance = undefined as any;
  }

  setDatabase(db: any): void {
    this.db = db;
    // 清除缓存的服务实例，强制重新创建
    this.services.clear();
  }



  /**
   * 获取用户配置服务
   */
  async getUserConfigService(): Promise<any> {
    if (!this.services.has('UserConfigService')) {
      const { UserConfigService } = await import('./entities/UserConfigService');
      this.services.set('UserConfigService', new UserConfigService(this.db));
    }
    return this.services.get('UserConfigService');
  }

  /**
   * 直接获取用户配置服务 (同步方法)
   */
  getUserConfigServiceSync(): any {
    if (!this.services.has('UserConfigService')) {
      const { UserConfigService } = require('./entities/UserConfigService');
      this.services.set('UserConfigService', new UserConfigService(this.db));
    }
    return this.services.get('UserConfigService');
  }

  /**
   * 获取心情追踪服务
   */
  async getMoodTrackingService(): Promise<any> {
    if (!this.services.has('MoodTrackingService')) {
      const { MoodTrackingService } = await import('./business/MoodTrackingService');
      this.services.set('MoodTrackingService', new MoodTrackingService());
    }
    return this.services.get('MoodTrackingService');
  }

  /**
   * 获取标签服务
   * 注意：使用版本替代
   */
  async getTagService(): Promise<any> {
    if (!this.services.has('TagService')) {
      const { TagService } = await import('./entities/TagService');
      this.services.set('TagService', new TagService());
    }
    return this.services.get('TagService');
  }



  /**
   * 获取皮肤服务
   * 注意：使用版本替代
   */
  async getSkinService(): Promise<any> {
    if (!this.services.has('SkinService')) {
      const { SkinService } = await import('./entities/SkinService');
      this.services.set('SkinService', new SkinService());
    }
    return this.services.get('SkinService');
  }

  /**
   * 获取UI标签服务
   * 注意：使用版本替代
   */
  async getUILabelService(): Promise<any> {
    if (!this.services.has('UILabelService')) {
      const { UILabelService } = await import('./entities/UILabelService');
      this.services.set('UILabelService', new UILabelService());
    }
    return this.services.get('UILabelService');
  }

  // MoodEntryTagService 不存在，暂时注释
  // /**
  //  * 获取心情标签关联服务
  //  */
  // async getMoodEntryTagService(): Promise<any> {
  //   if (!this.services.has('MoodEntryTagService')) {
  //     const { MoodEntryTagService } = await import('./entities/MoodEntryTagService');
  //     this.services.set('MoodEntryTagService', new MoodEntryTagService());
  //   }
  //   return this.services.get('MoodEntryTagService');
  // }



  /**
   * 获取Quiz包服务
   */
  async getQuizPackService(): Promise<any> {
    if (!this.services.has('QuizPackService')) {
      const { QuizPackService } = await import('./entities/QuizPackService');
      this.services.set('QuizPackService', new QuizPackService(this.db));
    }
    return this.services.get('QuizPackService');
  }

  /**
   * 直接获取Quiz包服务 (同步方法)
   */
  getQuizPackServiceSync(): any {
    if (!this.services.has('QuizPackService')) {
      const { QuizPackService } = require('./entities/QuizPackService');
      this.services.set('QuizPackService', new QuizPackService(this.db));
    }
    return this.services.get('QuizPackService');
  }

  /**
   * 获取Quiz引擎服务
   * 注意：使用QuizEngineV3替代QuizEngineV2
   */
  async getQuizEngineService(): Promise<any> {
    if (!this.services.has('QuizEngineV3')) {
      const { QuizEngineV3 } = await import('./entities/QuizEngineV3');
      const { QuizPackRepository } = await import('./entities/QuizPackRepository');
      const { QuizSessionRepository } = await import('./entities/QuizSessionRepository');
      const { QuizAnswerRepository } = await import('./entities/QuizAnswerRepository');

      const packRepo = new QuizPackRepository();
      const sessionRepo = new QuizSessionRepository();
      const answerRepo = new QuizAnswerRepository();

      this.services.set('QuizEngineV3', new QuizEngineV3(packRepo, sessionRepo, answerRepo));
    }
    return this.services.get('QuizEngineV3');
  }

  /**
   * 获取Quiz问题服务
   * 注意：使用版本替代
   */
  async getQuizQuestionService(): Promise<any> {
    if (!this.services.has('QuizQuestionService')) {
      const { QuizQuestionService } = await import('./entities/QuizQuestionService');
      this.services.set('QuizQuestionService', new QuizQuestionService());
    }
    return this.services.get('QuizQuestionService');
  }

  /**
   * 获取Quiz会话服务
   * 注意：使用版本替代
   */
  async getQuizSessionService(): Promise<any> {
    if (!this.services.has('QuizSessionService')) {
      const { QuizSessionService } = await import('./entities/QuizSessionService');
      this.services.set('QuizSessionService', new QuizSessionService());
    }
    return this.services.get('QuizSessionService');
  }

  /**
   * 获取Quiz答案服务
   * 注意：使用版本替代
   */
  async getQuizAnswerService(): Promise<any> {
    if (!this.services.has('QuizAnswerService')) {
      const { QuizAnswerService } = await import('./entities/QuizAnswerService');
      this.services.set('QuizAnswerService', new QuizAnswerService());
    }
    return this.services.get('QuizAnswerService');
  }

  /**
   * 获取全局应用配置服务
   */
  async getGlobalAppConfigService(): Promise<any> {
    if (!this.services.has('GlobalAppConfigService')) {
      const { GlobalAppConfigService } = await import('./entities/GlobalAppConfigService');
      const databaseService = await this.getDatabaseService();
      this.services.set('GlobalAppConfigService', new GlobalAppConfigService(databaseService));
    }
    return this.services.get('GlobalAppConfigService');
  }

  /**
   * 获取用户Quiz偏好配置服务
   */
  async getUserQuizPreferencesService(): Promise<any> {
    if (!this.services.has('UserQuizPreferencesService')) {
      const { UserQuizPreferencesService } = await import('./entities/UserQuizPreferencesService');
      const databaseService = await this.getDatabaseService();
      this.services.set('UserQuizPreferencesService', new UserQuizPreferencesService(databaseService));
    }
    return this.services.get('UserQuizPreferencesService');
  }

  /**
   * 获取Quiz包覆盖配置服务
   * 注意：使用Repository版本替代
   */
  async getQuizPackOverridesService(): Promise<any> {
    if (!this.services.has('QuizPackOverridesRepository')) {
      const { QuizPackOverridesRepository } = await import('./entities/QuizPackOverridesRepository');
      // QuizPackOverridesRepository extends BaseRepository and sets tableName internally
      const repository = new QuizPackOverridesRepository();
      this.services.set('QuizPackOverridesRepository', repository);
    }
    return this.services.get('QuizPackOverridesRepository');
  }

  /**
   * 获取Quiz配置合并服务
   */
  async getQuizConfigMergerService(): Promise<any> {
    if (!this.services.has('QuizConfigMergerService')) {
      const { QuizConfigMergerService } = await import('./entities/QuizConfigMergerService');
      const databaseService = await this.getDatabaseService();
      this.services.set('QuizConfigMergerService', new QuizConfigMergerService(databaseService));
    }
    return this.services.get('QuizConfigMergerService');
  }

  /**
   * 获取数据库服务
   */
  async getDatabaseService(): Promise<any> {
    const { DatabaseService } = await import('./base/DatabaseService');
    return DatabaseService.getInstance();
  }

  /**
   * 获取整合的数据库服务（包含四个核心服务功能）
   */
  async getIntegratedDatabaseService(): Promise<any> {
    const { DatabaseService } = await import('./base/DatabaseService');
    return DatabaseService.getInstance();
  }

  /**
   * 清理所有服务实例
   */
  clearServices(): void {
    this.services.clear();
  }

  /**
   * 注册自定义服务
   */
  registerService<T>(name: string, service: T): void {
    this.services.set(name, service);
  }

  /**
   * 获取自定义服务
   */
  getService<T>(name: string): T | undefined {
    return this.services.get(name);
  }
}

/**
 * 便捷的服务访问器
 * 只包含可用的服务
 */
export const Services = {
  // 可用的服务
  async userConfig() {
    return await ServiceFactory.getInstance().getUserConfigService();
  },

  async tag() {
    return await ServiceFactory.getInstance().getTagService();
  },

  async skin() {
    return await ServiceFactory.getInstance().getSkinService();
  },

  async uiLabel() {
    return await ServiceFactory.getInstance().getUILabelService();
  },

  async moodTracking() {
    return await ServiceFactory.getInstance().getMoodTrackingService();
  },

  async database() {
    return await ServiceFactory.getInstance().getDatabaseService();
  },

  async integratedDatabase() {
    return await ServiceFactory.getInstance().getIntegratedDatabaseService();
  },

  async quizPack() {
    return await ServiceFactory.getInstance().getQuizPackService();
  },

  async quizQuestion() {
    return await ServiceFactory.getInstance().getQuizQuestionService();
  },

  async quizEngine() {
    return await ServiceFactory.getInstance().getQuizEngineService();
  },

  async quizSession() {
    return await ServiceFactory.getInstance().getQuizSessionService();
  },

  async quizAnswer() {
    return await ServiceFactory.getInstance().getQuizAnswerService();
  },

  async globalAppConfig() {
    return await ServiceFactory.getInstance().getGlobalAppConfigService();
  },

  async userQuizPreferences() {
    return await ServiceFactory.getInstance().getUserQuizPreferencesService();
  },

  async quizPackOverrides() {
    return await ServiceFactory.getInstance().getQuizPackOverridesService();
  },

  async quizConfigMerger() {
    return await ServiceFactory.getInstance().getQuizConfigMergerService();
  }
};

/**
 * 服务初始化函数
 * 在应用启动时调用，确保所有服务正确初始化
 */
export async function initializeServices(): Promise<void> {
  try {
    // 使用整合的数据库服务进行初始化
    const integratedDbService = await ServiceFactory.getInstance().getIntegratedDatabaseService();
    await integratedDbService.initializeApp();

    // 检查连接状态
    const isConnected = await integratedDbService.checkConnection();
    if (!isConnected) {
      throw new Error('Database connection failed');
    }

    console.log('[Services] All services initialized successfully');
  } catch (error) {
    console.error('[Services] Failed to initialize services:', error);
    throw error;
  }
}

/**
 * 服务清理函数
 * 在应用关闭时调用，清理资源
 */
export async function cleanupServices(): Promise<void> {
  try {
    const databaseService = await ServiceFactory.getInstance().getDatabaseService();
    await databaseService.cleanup();

    ServiceFactory.getInstance().clearServices();

    console.log('Services cleaned up successfully');
  } catch (error) {
    console.error('Failed to cleanup services:', error);
  }
}



// 默认导出服务访问器
export default Services;
