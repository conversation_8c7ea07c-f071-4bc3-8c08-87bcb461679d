/**
 * Emoji映射服务
 * 管理不同Quiz包中选项的emoji展现映射
 */

import { UserQuizPreferencesRepository } from './UserQuizPreferencesRepository';
import { QuizPackOverridesRepository } from './QuizPackOverridesRepository';

export interface EmojiMapping {
  primary: string;
  alternatives: string[];
}

export interface EmotionPresentation {
  emoji_mapping: Record<string, EmojiMapping>;
  color_mapping: Record<string, string>;
  animation_mapping: Record<string, string>;
}

export interface EmojiMappingResult {
  emoji: string;
  color: string;
  animation: string;
  source: 'system' | 'user' | 'pack_override';
}

export class EmojiMappingService {
  private userPrefsRepo: UserQuizPreferencesRepository;
  private packOverridesRepo: QuizPackOverridesRepository;

  // 系统默认emoji映射
  private static readonly SYSTEM_DEFAULT_MAPPING: EmotionPresentation = {
    emoji_mapping: {
      happy: { primary: "😊", alternatives: ["😄", "😃", "🙂", "😌"] },
      sad: { primary: "😢", alternatives: ["😭", "😞", "☹️", "😔"] },
      angry: { primary: "😠", alternatives: ["😡", "🤬", "😤", "😒"] },
      fearful: { primary: "😨", alternatives: ["😰", "😱", "😧", "😟"] },
      surprised: { primary: "😲", alternatives: ["😮", "😯", "🤯", "😦"] },
      disgusted: { primary: "🤢", alternatives: ["🤮", "😖", "😣", "🙄"] },
      neutral: { primary: "😐", alternatives: ["😑", "😶", "🤔", "😏"] },
      excited: { primary: "🤩", alternatives: ["🎉", "⚡", "🚀", "✨"] },
      grateful: { primary: "🙏", alternatives: ["💖", "🌺", "🌸", "💝"] },
      playful: { primary: "😜", alternatives: ["😋", "🤪", "😝", "🤭"] },
      content: { primary: "😌", alternatives: ["😊", "🙂", "😇", "🥰"] }
    },
    color_mapping: {
      happy: "#4CAF50", sad: "#2196F3", angry: "#F44336", fearful: "#9C27B0",
      surprised: "#FF9800", disgusted: "#795548", neutral: "#9E9E9E",
      excited: "#FF6B35", grateful: "#E91E63", playful: "#9C27B0", content: "#4CAF50"
    },
    animation_mapping: {
      happy: "bounce", sad: "fade", angry: "shake", fearful: "tremble",
      surprised: "pop", disgusted: "recoil", neutral: "none",
      excited: "pulse", grateful: "glow", playful: "wiggle", content: "gentle"
    }
  };

  constructor(db?: any) {
    this.userPrefsRepo = new UserQuizPreferencesRepository('user_presentation_configs', db);
    this.packOverridesRepo = new QuizPackOverridesRepository('pack_presentation_overrides', db);
  }

  /**
   * 获取选项的emoji映射
   * 优先级: Pack Override > User Preference > System Default
   */
  async getOptionPresentation(
    userId: string,
    packId: string,
    optionValue: string
  ): Promise<EmojiMappingResult> {
    try {
      // 1. 尝试获取包特定覆盖
      const packOverride = await this.getPackOverrideMapping(userId, packId, optionValue);
      if (packOverride) {
        return { ...packOverride, source: 'pack_override' };
      }

      // 2. 尝试获取用户全局配置
      const userMapping = await this.getUserGlobalMapping(userId, optionValue);
      if (userMapping) {
        return { ...userMapping, source: 'user' };
      }

      // 3. 使用系统默认
      const systemMapping = this.getSystemDefaultMapping(optionValue);
      return { ...systemMapping, source: 'system' };

    } catch (error) {
      console.error('Error getting option presentation:', error);
      // 出错时返回系统默认
      const systemMapping = this.getSystemDefaultMapping(optionValue);
      return { ...systemMapping, source: 'system' };
    }
  }

  /**
   * 获取包特定的emoji覆盖
   */
  private async getPackOverrideMapping(
    userId: string,
    packId: string,
    optionValue: string
  ): Promise<Omit<EmojiMappingResult, 'source'> | null> {
    try {
      const override = await this.packOverridesRepo.findByUserAndPack(userId, packId);
      if (!override || !override.presentation_overrides) {
        return null;
      }

      const config = JSON.parse(override.presentation_overrides);
      const emotionPresentation = config?.layer4_view_detail?.emotion_presentation;
      
      if (!emotionPresentation) {
        return null;
      }

      return this.extractPresentationFromConfig(emotionPresentation, optionValue);
    } catch (error) {
      console.error('Error getting pack override mapping:', error);
      return null;
    }
  }

  /**
   * 获取用户全局emoji配置
   */
  private async getUserGlobalMapping(
    userId: string,
    optionValue: string
  ): Promise<Omit<EmojiMappingResult, 'source'> | null> {
    try {
      const userPrefs = await this.userPrefsRepo.findByUserId(userId);
      if (!userPrefs || !userPrefs.presentation_config) {
        return null;
      }

      const config = JSON.parse(userPrefs.presentation_config);
      const emotionPresentation = config?.layer4_view_detail?.emotion_presentation;
      
      if (!emotionPresentation) {
        return null;
      }

      return this.extractPresentationFromConfig(emotionPresentation, optionValue);
    } catch (error) {
      console.error('Error getting user global mapping:', error);
      return null;
    }
  }

  /**
   * 从配置中提取展现信息
   */
  private extractPresentationFromConfig(
    emotionPresentation: any,
    optionValue: string
  ): Omit<EmojiMappingResult, 'source'> | null {
    const emojiMapping = emotionPresentation.emoji_mapping?.[optionValue];
    const color = emotionPresentation.color_mapping?.[optionValue];
    const animation = emotionPresentation.animation_mapping?.[optionValue];

    if (!emojiMapping?.primary) {
      return null;
    }

    return {
      emoji: emojiMapping.primary,
      color: color || EmojiMappingService.SYSTEM_DEFAULT_MAPPING.color_mapping[optionValue] || '#9E9E9E',
      animation: animation || EmojiMappingService.SYSTEM_DEFAULT_MAPPING.animation_mapping[optionValue] || 'none'
    };
  }

  /**
   * 获取系统默认映射
   */
  private getSystemDefaultMapping(optionValue: string): Omit<EmojiMappingResult, 'source'> {
    const defaultMapping = EmojiMappingService.SYSTEM_DEFAULT_MAPPING;
    
    return {
      emoji: defaultMapping.emoji_mapping[optionValue]?.primary || '📝',
      color: defaultMapping.color_mapping[optionValue] || '#9E9E9E',
      animation: defaultMapping.animation_mapping[optionValue] || 'none'
    };
  }

  /**
   * 更新用户的emoji映射配置
   */
  async updateUserEmojiMapping(
    userId: string,
    optionValue: string,
    mapping: EmojiMapping,
    color?: string,
    animation?: string
  ): Promise<boolean> {
    try {
      const userPrefs = await this.userPrefsRepo.findByUserId(userId);
      if (!userPrefs) {
        return false;
      }

      const config = JSON.parse(userPrefs.presentation_config);
      
      // 确保结构存在
      if (!config.layer4_view_detail) {
        config.layer4_view_detail = {};
      }
      if (!config.layer4_view_detail.emotion_presentation) {
        config.layer4_view_detail.emotion_presentation = {
          emoji_mapping: {},
          color_mapping: {},
          animation_mapping: {}
        };
      }

      // 更新映射
      config.layer4_view_detail.emotion_presentation.emoji_mapping[optionValue] = mapping;
      
      if (color) {
        config.layer4_view_detail.emotion_presentation.color_mapping[optionValue] = color;
      }
      
      if (animation) {
        config.layer4_view_detail.emotion_presentation.animation_mapping[optionValue] = animation;
      }

      // 保存更新
      await this.userPrefsRepo.update(userPrefs.id, {
        presentation_config: JSON.stringify(config)
      });

      return true;
    } catch (error) {
      console.error('Error updating user emoji mapping:', error);
      return false;
    }
  }

  /**
   * 更新包特定的emoji映射覆盖
   */
  async updatePackEmojiOverride(
    userId: string,
    packId: string,
    optionValue: string,
    mapping: EmojiMapping,
    color?: string,
    animation?: string
  ): Promise<boolean> {
    try {
      let override = await this.packOverridesRepo.findByUserAndPack(userId, packId);
      
      let config: any = {};
      if (override?.presentation_overrides) {
        config = JSON.parse(override.presentation_overrides);
      }

      // 确保结构存在
      if (!config.layer4_view_detail) {
        config.layer4_view_detail = {};
      }
      if (!config.layer4_view_detail.emotion_presentation) {
        config.layer4_view_detail.emotion_presentation = {
          emoji_mapping: {},
          color_mapping: {},
          animation_mapping: {}
        };
      }

      // 更新映射
      config.layer4_view_detail.emotion_presentation.emoji_mapping[optionValue] = mapping;
      
      if (color) {
        config.layer4_view_detail.emotion_presentation.color_mapping[optionValue] = color;
      }
      
      if (animation) {
        config.layer4_view_detail.emotion_presentation.animation_mapping[optionValue] = animation;
      }

      if (override) {
        // 更新现有覆盖
        await this.packOverridesRepo.update(override.id, {
          presentation_overrides: JSON.stringify(config)
        });
      } else {
        // 创建新覆盖
        await this.packOverridesRepo.create({
          id: `override_${userId}_${packId}_${Date.now()}`,
          user_id: userId,
          pack_id: packId,
          presentation_overrides: JSON.stringify(config),
          override_reason: 'user_preference',
          override_priority: 1,
          is_active: true
        });
      }

      return true;
    } catch (error) {
      console.error('Error updating pack emoji override:', error);
      return false;
    }
  }

  /**
   * 获取选项的所有可用emoji选择
   */
  async getAvailableEmojis(
    userId: string,
    packId: string,
    optionValue: string
  ): Promise<string[]> {
    try {
      const presentation = await this.getOptionPresentation(userId, packId, optionValue);
      const systemMapping = EmojiMappingService.SYSTEM_DEFAULT_MAPPING.emoji_mapping[optionValue];
      
      if (systemMapping) {
        return [presentation.emoji, ...systemMapping.alternatives].filter((emoji, index, arr) => 
          arr.indexOf(emoji) === index // 去重
        );
      }

      return [presentation.emoji];
    } catch (error) {
      console.error('Error getting available emojis:', error);
      return ['📝'];
    }
  }

  /**
   * 设置数据库连接
   */
  setDb(db: any): void {
    this.userPrefsRepo.setDatabase(db);
    this.packOverridesRepo.setDatabase(db);
  }
}
