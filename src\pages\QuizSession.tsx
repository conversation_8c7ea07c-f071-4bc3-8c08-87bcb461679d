/**
 * Quiz会话执行页面
 *
 * 负责Quiz的实际执行，包括问题展示、用户交互和进度管理
 * 集成ViewFactory和特殊视图组件
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  EmotionWheelView,
  EmotionCardView,
  EmotionBubbleView,
  SpecialViewFactory,
  type EmotionItem,
  type EmotionCardData,
  type EmotionBubbleData
} from '../components/quiz/special-views';
// 临时定义类型，直到base.ts中有这些类型
interface PersonalizationConfig {
  user_type: string;
  language: string;
  theme: string;
  accessibility: {
    high_contrast: boolean;
    large_text: boolean;
    screen_reader: boolean;
    keyboard_navigation: boolean;
  };
  cultural_preferences: {
    tcm_elements: boolean;
    traditional_colors: boolean;
    simplified_interface: boolean;
  };
}

interface InteractionEvent {
  type: string;
  data: any;
}
import { Card, CardContent } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Progress } from '../components/ui/progress';
import { AlertCircle, ArrowLeft, SkipForward, CheckCircle } from 'lucide-react';
import { useCurrentQuestion, useSubmitAnswer } from '../hooks/useQuiz';

interface QuizSessionProps {
  sessionId?: string;
}

interface RealtimeInsight {
  type: string;
  message: string;
  confidence: number;
}

const QuizSession: React.FC<QuizSessionProps> = () => {
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();

  // 状态管理
  const [selectedEmotions, setSelectedEmotions] = useState<string[]>([]);
  const [realtimeInsights, setRealtimeInsights] = useState<RealtimeInsight[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [sessionCompleted, setSessionCompleted] = useState(false);

  // 使用新的Quiz hooks
  const {
    data: questionResponse,
    isLoading: isLoadingQuestion,
    refresh: refetchQuestion // 使用正确的方法名
  } = useCurrentQuestion(sessionId || '');

  const submitAnswerMutation = useSubmitAnswer();

  // 处理提交答案的成功回调
  useEffect(() => {
    if (submitAnswerMutation.isSuccess && submitAnswerMutation.data) {
      const result = submitAnswerMutation.data;

      if (result.success) {
        setRealtimeInsights(result.data?.realtime_insights || []);

        if (result.data?.progress_update?.completion_percentage === 100) {
          setSessionCompleted(true);
          // 延迟跳转到结果页面
          setTimeout(() => {
            navigate(`/quiz-results/${sessionId}`);
          }, 2000);
        } else {
          // 清空选择并获取下一个问题
          setSelectedEmotions([]);
          refetchQuestion();
        }
      }
      setIsSubmitting(false);
    }

    if (submitAnswerMutation.isError) {
      console.error('Failed to submit answer:', submitAnswerMutation.error);
      setIsSubmitting(false);
    }
  }, [submitAnswerMutation.isSuccess, submitAnswerMutation.isError, submitAnswerMutation.data, navigate, sessionId, refetchQuestion]);

  // 从响应中提取问题数据
  const questionData = questionResponse?.success ? questionResponse.data : null;

  // 个性化配置
  const personalizationConfig: PersonalizationConfig = {
    user_type: 'general',
    language: 'zh',
    theme: 'light', // 可以从用户配置中获取
    accessibility: {
      high_contrast: false,
      large_text: false,
      screen_reader: false,
      keyboard_navigation: false
    },
    cultural_preferences: {
      tcm_elements: true,
      traditional_colors: false,
      simplified_interface: false
    }
  };

  // 处理情绪选择
  const handleEmotionSelect = useCallback((emotionId: string, _emotion: any) => {
    const questionType = questionData?.question_type || 'single_choice';

    if (questionType === 'single_choice' || questionType === 'emotion_wheel') {
      // 单选类型
      setSelectedEmotions([emotionId]);
    } else {
      // 多选类型
      setSelectedEmotions(prev =>
        prev.includes(emotionId)
          ? prev.filter(id => id !== emotionId)
          : [...prev, emotionId]
      );
    }
  }, [questionData?.question_type]);

  const handleEmotionDeselect = useCallback((emotionId: string) => {
    setSelectedEmotions(prev => prev.filter(id => id !== emotionId));
  }, []);

  // 处理交互事件
  const handleInteraction = useCallback((event: InteractionEvent) => {
    console.log('Quiz interaction:', event);
    // 可以添加分析和日志记录
  }, []);

  // 提交答案
  const handleSubmitAnswer = useCallback(async () => {
    if (selectedEmotions.length === 0 || !questionData || !sessionId) return;

    setIsSubmitting(true);

    try {
      // 使用新的答案提交格式
      await submitAnswerMutation.mutateAsync({
        session_id: sessionId,
        question_id: questionData.question_id,
        selected_option_ids: selectedEmotions,
        answer_value: selectedEmotions[0], // 主要答案值
        confidence_score: 80, // 0-100 范围
        response_time_ms: Date.now() - (questionData as any).startTime || 0
      });
    } catch (error) {
      console.error('Failed to submit answer:', error);
      setIsSubmitting(false);
    }
  }, [selectedEmotions, questionData, sessionId, submitAnswerMutation]);

  // 跳过问题
  const handleSkipQuestion = useCallback(() => {
    if (!questionData || !sessionId) return;

    // 可以实现跳过逻辑
    console.log('Skip question:', questionData.question_id);
  }, [questionData, sessionId]);

  // 返回上一题
  const handleGoBack = useCallback(() => {
    // 可以实现返回逻辑
    console.log('Go back');
  }, []);

  // 转换问题选项数据为特殊视图格式
  const convertToWheelEmotions = useCallback((): EmotionItem[] => {
    if (!questionData?.question_options) return [];

    return questionData.question_options.map((option: any) => ({
      id: option.option_id,
      name: {
        zh: JSON.parse(option.option_text_localized || '{}').zh || option.option_text,
        en: JSON.parse(option.option_text_localized || '{}').en || option.option_text
      },
      color: option.constraints?.color || '#3B82F6',
      emoji: option.constraints?.emoji || '😊',
      tier_level: 1
    }));
  }, [questionData]);

  const convertToCardEmotions = useCallback((): EmotionCardData[] => {
    if (!questionData?.question_options) return [];

    return questionData.question_options.map((option: any) => ({
      id: option.option_id,
      name: {
        zh: JSON.parse(option.option_text_localized || '{}').zh || option.option_text,
        en: JSON.parse(option.option_text_localized || '{}').en || option.option_text
      },
      emoji: option.constraints?.emoji || '😊',
      color: option.constraints?.color || '#3B82F6',
      intensity: option.scoring_value || 3,
      category: '选项'
    }));
  }, [questionData]);

  const convertToBubbleEmotions = useCallback((): EmotionBubbleData[] => {
    if (!questionData?.question_options) return [];

    return questionData.question_options.map((option: any) => ({
      id: option.option_id,
      name: {
        zh: JSON.parse(option.option_text_localized || '{}').zh || option.option_text,
        en: JSON.parse(option.option_text_localized || '{}').en || option.option_text
      },
      emoji: option.constraints?.emoji || '😊',
      color: option.constraints?.color || '#3B82F6',
      intensity: (option.scoring_value || 3) / 5,
      frequency: 0.5,
      category: '选项'
    }));
  }, [questionData]);

  // 渲染问题视图
  const renderQuestionView = () => {
    if (!questionData) return null;

    // 根据问题类型决定视图类型
    const questionType = questionData.question_type;
    let viewType = 'card'; // 默认视图

    if (questionType === 'emotion_wheel') {
      viewType = 'wheel';
    } else if (questionType === 'scale_rating') {
      viewType = 'card';
    }

    switch (viewType) {
      case 'wheel':
        return (
          <EmotionWheelView
            id="quiz-emotion-wheel"
            emotions={convertToWheelEmotions()}
            selectedEmotions={selectedEmotions}
            onEmotionSelect={handleEmotionSelect}
            config={SpecialViewFactory.createEmotionWheelConfig(
              400, // 默认容器大小
              60,
              80
            )}
            personalization={personalizationConfig}
            onInteraction={handleInteraction}
            language="zh"
          />
        );

      case 'card':
        return (
          <EmotionCardView
            id="quiz-emotion-cards"
            emotions={convertToCardEmotions()}
            selectedEmotions={selectedEmotions}
            onEmotionSelect={handleEmotionSelect}
            onEmotionDeselect={handleEmotionDeselect}
            config={SpecialViewFactory.createEmotionCardConfig(3, 180, 150)}
            personalization={personalizationConfig}
            onInteraction={handleInteraction}
            language="zh"
            maxSelections={3}
          />
        );

      case 'bubble':
        return (
          <EmotionBubbleView
            id="quiz-emotion-bubbles"
            emotions={convertToBubbleEmotions()}
            selectedEmotions={selectedEmotions}
            onEmotionSelect={handleEmotionSelect}
            onEmotionDeselect={handleEmotionDeselect}
            config={SpecialViewFactory.createEmotionBubbleConfig(600, 400)}
            personalization={personalizationConfig}
            onInteraction={handleInteraction}
            language="zh"
          />
        );

      default:
        return (
          <div className="text-center p-8">
            <AlertCircle className="mx-auto h-12 w-12 text-yellow-500 mb-4" />
            <p className="text-gray-600">不支持的视图类型: {viewType}</p>
          </div>
        );
    }
  };

  // 加载状态
  if (isLoadingQuestion) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载问题...</p>
        </div>
      </div>
    );
  }

  // 会话完成状态
  if (sessionCompleted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <CheckCircle className="mx-auto h-16 w-16 text-green-500 mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">测评完成！</h2>
            <p className="text-gray-600 mb-4">
              感谢您完成情绪测评，正在为您生成分析报告...
            </p>
            <div className="animate-pulse">
              <div className="h-2 bg-gray-200 rounded mb-2"></div>
              <div className="h-2 bg-gray-200 rounded w-3/4 mx-auto"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 没有问题数据
  if (!questionData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <AlertCircle className="mx-auto h-12 w-12 text-yellow-500 mb-4" />
            <h2 className="text-xl font-bold text-gray-900 mb-2">会话已结束</h2>
            <p className="text-gray-600 mb-4">
              当前会话已完成或不存在
            </p>
            <Button onClick={() => navigate('/quiz-settings')}>
              返回设置
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部进度条 */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between mb-2">
            <h1 className="text-lg font-semibold text-gray-900">情绪测评</h1>
            <span className="text-sm text-gray-500">
              {questionData.progress_info.current_question} / {questionData.progress_info.total_questions}
            </span>
          </div>
          <Progress
            value={questionData.progress_info.completion_percentage}
            className="h-2"
          />
          {questionData.progress_info.estimated_remaining_time && (
            <p className="text-xs text-gray-500 mt-1">
              预计剩余时间: {questionData.progress_info.estimated_remaining_time}
            </p>
          )}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 问题文本 */}
        <Card className="mb-8">
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-medium text-gray-900 mb-2">
              {questionData.question_text_localized || questionData.question_text}
            </h2>
            <p className="text-gray-600">
              请选择最能描述您当前状态的选项
            </p>
          </CardContent>
        </Card>

        {/* 问题视图 */}
        <div className="mb-8 flex justify-center">
          {renderQuestionView()}
        </div>

        {/* 实时洞察 */}
        {realtimeInsights.length > 0 && (
          <Card className="mb-8">
            <CardContent className="p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-2">实时反馈</h3>
              {realtimeInsights.map((insight, index) => (
                <div key={index} className="text-sm text-gray-600 mb-1">
                  {insight.message}
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* 底部操作栏 */}
        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            {questionData.navigation_config.show_back_button && (
              <Button
                variant="outline"
                onClick={handleGoBack}
                disabled={isSubmitting}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                上一题
              </Button>
            )}

            {questionData.navigation_config.show_skip_button && (
              <Button
                variant="outline"
                onClick={handleSkipQuestion}
                disabled={isSubmitting}
              >
                <SkipForward className="h-4 w-4 mr-2" />
                跳过
              </Button>
            )}
          </div>

          <Button
            onClick={handleSubmitAnswer}
            disabled={selectedEmotions.length === 0 || isSubmitting}
            className="min-w-[120px]"
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                提交中...
              </div>
            ) : (
              '下一步'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default QuizSession;
