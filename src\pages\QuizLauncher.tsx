/**
 * Quiz启动页面
 *
 * 展示可用的Quiz包，允许用户选择并启动新的Quiz会话
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
// import { trpc } from '../lib/trpc'; // 暂时注释，等待tRPC路由集成
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { Input } from '../components/ui/input';
import {
  Search,
  Clock,
  Users,
  Star,
  Play,
  Filter,
  TrendingUp,
  Heart,
  Brain,
  Target
} from 'lucide-react';
import { useQuizPacks, useCreateQuizSession } from '../hooks/useQuiz';

const QuizLauncher: React.FC = () => {
  const navigate = useNavigate();

  // 状态管理
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all');

  // 使用新的Quiz hooks
  const {
    data: quizPacksData,
    isLoading: isLoadingPacks
  } = useQuizPacks({
    category: selectedCategory === 'all' ? undefined : selectedCategory,
    difficultyLevel: selectedDifficulty === 'all' ? undefined : parseInt(selectedDifficulty),
    limit: 20
  });

  // 注意：需要等待tRPC路由正确集成后才能使用
  // const {
  //   data: recommendationsData
  // } = trpc.getRecommendedQuizPacks.useQuery();

  // 创建会话变更
  const createSessionMutation = useCreateQuizSession();

  // 处理Quiz启动
  const handleStartQuiz = async (packId: string) => {
    try {
      const result = await createSessionMutation.mutateAsync({
        packId: packId,
        userId: 'current-user' // 这里应该从认证上下文获取
      });

      if (result.success) {
        navigate(`/quiz-session/${result.data.id}`);
      } else {
        console.error('Failed to create session:', result.error);
      }
    } catch (error) {
      console.error('Failed to create session:', error);
    }
  };

  // 过滤Quiz包
  const filteredPacks = quizPacksData?.data?.filter((pack: any) => {
    const matchesSearch = searchQuery === '' ||
      pack.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (pack.description && pack.description.toLowerCase().includes(searchQuery.toLowerCase()));

    return matchesSearch;
  }) || [];

  // 获取分类图标
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'daily': return <Heart className="h-4 w-4" />;
      case 'therapy': return <Brain className="h-4 w-4" />;
      case 'assessment': return <TrendingUp className="h-4 w-4" />;
      case 'research': return <Target className="h-4 w-4" />;
      default: return <Star className="h-4 w-4" />;
    }
  };

  // 获取难度颜色
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'regular': return 'bg-blue-100 text-blue-800';
      case 'advanced': return 'bg-purple-100 text-purple-800';
      case 'expert': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 渲染Quiz包卡片
  const renderQuizPackCard = (pack: any) => (
    <Card key={pack.id} className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            {getCategoryIcon(pack.category || 'daily')}
            <CardTitle className="text-lg">{pack.name}</CardTitle>
          </div>
          <div className="flex space-x-2">
            <Badge variant="outline" className={getDifficultyColor(pack.difficulty_level?.toString() || 'regular')}>
              {pack.difficulty_level || 'regular'}
            </Badge>
            {pack.tags?.includes('featured') && (
              <Badge variant="default">
                <Star className="h-3 w-3 mr-1" />
                推荐
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600 mb-4 line-clamp-2">
          {pack.description}
        </p>

        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              {pack.estimated_duration_minutes || 10}分钟
            </div>
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-1" />
              {pack.stats?.total_sessions || 0}人完成
            </div>
          </div>
          <div className="flex items-center">
            <Star className="h-4 w-4 mr-1 text-yellow-500" />
            {pack.stats?.average_rating || 4.5}
          </div>
        </div>

        <Button
          onClick={() => handleStartQuiz(pack.id)}
          disabled={createSessionMutation.isPending}
          className="w-full"
        >
          <Play className="h-4 w-4 mr-2" />
          {createSessionMutation.isPending ? '启动中...' : '开始测评'}
        </Button>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部标题 */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">情绪测评中心</h1>
            <p className="text-gray-600 max-w-2xl mx-auto">
              选择适合您的情绪测评量表，开始了解和管理您的情绪状态
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* 搜索和过滤 */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索测评量表..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="flex gap-2">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">所有分类</option>
                <option value="daily">日常情绪</option>
                <option value="therapy">治疗评估</option>
                <option value="assessment">专业评估</option>
                <option value="research">研究用途</option>
              </select>

              <select
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">所有难度</option>
                <option value="beginner">入门</option>
                <option value="regular">标准</option>
                <option value="advanced">进阶</option>
                <option value="expert">专家</option>
              </select>
            </div>
          </div>
        </div>

        {/* 推荐测评 - 暂时注释，等待tRPC路由集成 */}
        {/* {recommendationsData?.data && recommendationsData.data.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">为您推荐</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recommendationsData.data.slice(0, 3).map((pack: any) =>
                renderQuizPackCard(pack)
              )}
            </div>
          </div>
        )} */}

        {/* 所有测评 */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">所有测评</h2>
            <div className="flex items-center text-sm text-gray-500">
              <Filter className="h-4 w-4 mr-1" />
              {filteredPacks.length} 个结果
            </div>
          </div>

          {isLoadingPacks ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, index) => (
                <Card key={index} className="animate-pulse">
                  <CardHeader>
                    <div className="h-6 bg-gray-200 rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
                    <div className="h-10 bg-gray-200 rounded"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredPacks.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-gray-400 mb-4">
                  <Search className="mx-auto h-12 w-12" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的测评</h3>
                <p className="text-gray-600">
                  请尝试调整搜索条件或过滤器
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPacks.map((pack: any) => renderQuizPackCard(pack))}
            </div>
          )}
        </div>

        {/* 底部信息 */}
        <div className="mt-12 text-center">
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-medium text-blue-900 mb-2">需要帮助？</h3>
            <p className="text-blue-700 mb-4">
              如果您不确定选择哪个测评，我们建议从"日常情绪"分类开始
            </p>
            <Button variant="outline" onClick={() => navigate('/quiz-settings')}>
              查看个性化设置
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizLauncher;
