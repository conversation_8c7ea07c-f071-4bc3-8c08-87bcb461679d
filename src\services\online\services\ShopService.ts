/**
 * 在线商店服务
 * 处理皮肤、表情符号集、VIP订阅等商品的统一管理
 */

import { trpc } from '@/lib/trpc';

export interface ShopItem {
  id: string;
  type: 'skin' | 'emoji_set' | 'vip_subscription' | 'quiz_pack';
  name: string;
  description: string;
  price: number;
  currency: 'USD' | 'CNY' | 'points';
  category: string;
  tags: string[];
  isUnlocked: boolean;
  isPurchased: boolean;
  isOnSale: boolean;
  originalPrice?: number;
  discountPercentage?: number;
  previewUrl?: string;
  thumbnailUrl?: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  unlockRequirements?: {
    level?: number;
    achievements?: string[];
    prerequisites?: string[];
  };
  metadata: any;
}

export interface ShopFilters {
  type?: string[];
  category?: string[];
  priceRange?: { min: number; max: number };
  currency?: string;
  rarity?: string[];
  isUnlocked?: boolean;
  isPurchased?: boolean;
  isOnSale?: boolean;
  searchQuery?: string;
}

export interface PurchaseRequest {
  itemId: string;
  userId: string;
  paymentMethod?: 'stripe' | 'points' | 'free';
  couponCode?: string;
}

export class ShopService {
  private trpcClient: typeof trpc;

  constructor(trpcClient: typeof trpc) {
    this.trpcClient = trpcClient;
  }

  /**
   * 获取所有可用的商店物品
   */
  async getAvailableItems(
    userId: string,
    filters?: ShopFilters
  ): Promise<{ success: boolean; data?: ShopItem[]; error?: string }> {
    try {
      console.log('[ShopService] Getting available items:', { userId, filters });

      const result = await this.trpcClient.shop.getAvailableItems.query({
        userId,
        filters: filters || {}
      });

      if (result.success) {
        console.log('[ShopService] Available items retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[ShopService] Failed to get available items:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ShopService] Error getting available items:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 获取用户已购买的物品
   */
  async getUserPurchases(
    userId: string,
    itemType?: string
  ): Promise<{ success: boolean; data?: ShopItem[]; error?: string }> {
    try {
      console.log('[ShopService] Getting user purchases:', { userId, itemType });

      const result = await this.trpcClient.shop.getUserPurchases.query({
        userId,
        itemType
      });

      if (result.success) {
        console.log('[ShopService] User purchases retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[ShopService] Failed to get user purchases:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ShopService] Error getting user purchases:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 获取特色商品
   */
  async getFeaturedItems(
    userId: string,
    limit: number = 10
  ): Promise<{ success: boolean; data?: ShopItem[]; error?: string }> {
    try {
      console.log('[ShopService] Getting featured items:', { userId, limit });

      const result = await this.trpcClient.shop.getFeaturedItems.query({
        userId,
        limit
      });

      if (result.success) {
        console.log('[ShopService] Featured items retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[ShopService] Failed to get featured items:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ShopService] Error getting featured items:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 获取促销商品
   */
  async getSaleItems(
    userId: string,
    category?: string
  ): Promise<{ success: boolean; data?: ShopItem[]; error?: string }> {
    try {
      console.log('[ShopService] Getting sale items:', { userId, category });

      const result = await this.trpcClient.shop.getSaleItems.query({
        userId,
        category
      });

      if (result.success) {
        console.log('[ShopService] Sale items retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[ShopService] Failed to get sale items:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ShopService] Error getting sale items:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 购买商品
   */
  async purchaseItem(
    request: PurchaseRequest
  ): Promise<{ success: boolean; data?: { transactionId: string; item: ShopItem }; error?: string }> {
    try {
      console.log('[ShopService] Purchasing item:', request);

      const result = await this.trpcClient.shop.purchaseItem.mutate(request);

      if (result.success) {
        console.log('[ShopService] Item purchased successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[ShopService] Failed to purchase item:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ShopService] Error purchasing item:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 验证优惠券
   */
  async validateCoupon(
    couponCode: string,
    itemId: string,
    userId: string
  ): Promise<{ success: boolean; data?: { isValid: boolean; discount: number; message: string }; error?: string }> {
    try {
      console.log('[ShopService] Validating coupon:', { couponCode, itemId, userId });

      const result = await this.trpcClient.shop.validateCoupon.query({
        couponCode,
        itemId,
        userId
      });

      if (result.success) {
        console.log('[ShopService] Coupon validation completed');
        return { success: true, data: result.data };
      } else {
        console.warn('[ShopService] Failed to validate coupon:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ShopService] Error validating coupon:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 获取用户积分余额
   */
  async getUserPoints(
    userId: string
  ): Promise<{ success: boolean; data?: { points: number; earnedToday: number; totalEarned: number }; error?: string }> {
    try {
      console.log('[ShopService] Getting user points:', userId);

      const result = await this.trpcClient.shop.getUserPoints.query({
        userId
      });

      if (result.success) {
        console.log('[ShopService] User points retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[ShopService] Failed to get user points:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ShopService] Error getting user points:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 获取商品详情
   */
  async getItemDetails(
    itemId: string,
    userId: string
  ): Promise<{ success: boolean; data?: ShopItem & { relatedItems: ShopItem[] }; error?: string }> {
    try {
      console.log('[ShopService] Getting item details:', { itemId, userId });

      const result = await this.trpcClient.shop.getItemDetails.query({
        itemId,
        userId
      });

      if (result.success) {
        console.log('[ShopService] Item details retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[ShopService] Failed to get item details:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ShopService] Error getting item details:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 获取购买历史
   */
  async getPurchaseHistory(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ success: boolean; data?: Array<{ transactionId: string; item: ShopItem; purchaseDate: string; amount: number }>; error?: string }> {
    try {
      console.log('[ShopService] Getting purchase history:', { userId, limit, offset });

      const result = await this.trpcClient.shop.getPurchaseHistory.query({
        userId,
        limit,
        offset
      });

      if (result.success) {
        console.log('[ShopService] Purchase history retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[ShopService] Failed to get purchase history:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ShopService] Error getting purchase history:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 获取推荐商品
   */
  async getRecommendedItems(
    userId: string,
    limit: number = 10
  ): Promise<{ success: boolean; data?: ShopItem[]; error?: string }> {
    try {
      console.log('[ShopService] Getting recommended items:', { userId, limit });

      const result = await this.trpcClient.shop.getRecommendedItems.query({
        userId,
        limit
      });

      if (result.success) {
        console.log('[ShopService] Recommended items retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[ShopService] Failed to get recommended items:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ShopService] Error getting recommended items:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
}

export default ShopService;
