/**
 * 在线分析服务
 * 处理用户数据分析、趋势分析等服务器端聚合操作
 */

import { trpc } from '@/lib/trpc';

export interface UserTrends {
  moodTrends: {
    daily: Array<{ date: string; averageMood: number; entryCount: number }>;
    weekly: Array<{ week: string; averageMood: number; entryCount: number }>;
    monthly: Array<{ month: string; averageMood: number; entryCount: number }>;
  };
  emotionDistribution: Array<{ emotion: string; count: number; percentage: number }>;
  quizPerformance: {
    completionRate: number;
    averageScore: number;
    totalQuizzes: number;
    favoriteCategories: Array<{ category: string; count: number }>;
  };
  streaks: {
    currentStreak: number;
    longestStreak: number;
    lastEntryDate: string;
  };
  insights: Array<{
    type: 'improvement' | 'concern' | 'achievement';
    message: string;
    confidence: number;
  }>;
}

export interface AnalyticsFilters {
  startDate?: string;
  endDate?: string;
  categories?: string[];
  timeGranularity?: 'daily' | 'weekly' | 'monthly';
  includeQuizData?: boolean;
  includeMoodData?: boolean;
}

export class AnalyticsService {
  private trpcClient: typeof trpc;

  constructor(trpcClient: typeof trpc) {
    this.trpcClient = trpcClient;
  }

  /**
   * 获取用户趋势分析
   */
  async getUserTrends(
    userId: string,
    filters?: AnalyticsFilters
  ): Promise<{ success: boolean; data?: UserTrends; error?: string }> {
    try {
      console.log('[AnalyticsService] Getting user trends:', { userId, filters });

      const result = await this.trpcClient.analytics.getUserTrends.query({
        userId,
        filters: filters || {}
      });

      if (result.success) {
        console.log('[AnalyticsService] User trends retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[AnalyticsService] Failed to get user trends:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[AnalyticsService] Error getting user trends:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 获取情绪分布统计
   */
  async getEmotionDistribution(
    userId: string,
    timeRange: 'week' | 'month' | 'quarter' | 'year' = 'month'
  ): Promise<{ success: boolean; data?: Array<{ emotion: string; count: number; percentage: number }>; error?: string }> {
    try {
      console.log('[AnalyticsService] Getting emotion distribution:', { userId, timeRange });

      const result = await this.trpcClient.analytics.getEmotionDistribution.query({
        userId,
        timeRange
      });

      if (result.success) {
        console.log('[AnalyticsService] Emotion distribution retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[AnalyticsService] Failed to get emotion distribution:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[AnalyticsService] Error getting emotion distribution:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 获取心情趋势数据
   */
  async getMoodTrends(
    userId: string,
    granularity: 'daily' | 'weekly' | 'monthly' = 'daily',
    days: number = 30
  ): Promise<{ success: boolean; data?: Array<{ date: string; averageMood: number; entryCount: number }>; error?: string }> {
    try {
      console.log('[AnalyticsService] Getting mood trends:', { userId, granularity, days });

      const result = await this.trpcClient.analytics.getMoodTrends.query({
        userId,
        granularity,
        days
      });

      if (result.success) {
        console.log('[AnalyticsService] Mood trends retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[AnalyticsService] Failed to get mood trends:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[AnalyticsService] Error getting mood trends:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 获取Quiz表现统计
   */
  async getQuizPerformance(
    userId: string,
    timeRange: 'week' | 'month' | 'quarter' | 'year' = 'month'
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.log('[AnalyticsService] Getting quiz performance:', { userId, timeRange });

      const result = await this.trpcClient.analytics.getQuizPerformance.query({
        userId,
        timeRange
      });

      if (result.success) {
        console.log('[AnalyticsService] Quiz performance retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[AnalyticsService] Failed to get quiz performance:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[AnalyticsService] Error getting quiz performance:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 获取用户活动连续性统计
   */
  async getUserStreaks(
    userId: string
  ): Promise<{ success: boolean; data?: { currentStreak: number; longestStreak: number; lastEntryDate: string }; error?: string }> {
    try {
      console.log('[AnalyticsService] Getting user streaks:', userId);

      const result = await this.trpcClient.analytics.getUserStreaks.query({
        userId
      });

      if (result.success) {
        console.log('[AnalyticsService] User streaks retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[AnalyticsService] Failed to get user streaks:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[AnalyticsService] Error getting user streaks:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 生成个性化洞察
   */
  async generateInsights(
    userId: string,
    analysisType: 'mood' | 'quiz' | 'comprehensive' = 'comprehensive'
  ): Promise<{ success: boolean; data?: Array<{ type: string; message: string; confidence: number }>; error?: string }> {
    try {
      console.log('[AnalyticsService] Generating insights:', { userId, analysisType });

      const result = await this.trpcClient.analytics.generateInsights.mutate({
        userId,
        analysisType
      });

      if (result.success) {
        console.log('[AnalyticsService] Insights generated successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[AnalyticsService] Failed to generate insights:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[AnalyticsService] Error generating insights:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 导出用户数据
   */
  async exportUserData(
    userId: string,
    format: 'json' | 'csv' | 'pdf' = 'json',
    includeAnalytics: boolean = true
  ): Promise<{ success: boolean; data?: { downloadUrl: string; expiresAt: string }; error?: string }> {
    try {
      console.log('[AnalyticsService] Exporting user data:', { userId, format, includeAnalytics });

      const result = await this.trpcClient.analytics.exportUserData.mutate({
        userId,
        format,
        includeAnalytics
      });

      if (result.success) {
        console.log('[AnalyticsService] User data export initiated successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[AnalyticsService] Failed to export user data:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[AnalyticsService] Error exporting user data:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 获取比较分析（与其他用户或历史数据比较）
   */
  async getComparativeAnalysis(
    userId: string,
    comparisonType: 'historical' | 'anonymous_aggregate' = 'historical',
    timeRange: 'month' | 'quarter' | 'year' = 'month'
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.log('[AnalyticsService] Getting comparative analysis:', { 
        userId, 
        comparisonType, 
        timeRange 
      });

      const result = await this.trpcClient.analytics.getComparativeAnalysis.query({
        userId,
        comparisonType,
        timeRange
      });

      if (result.success) {
        console.log('[AnalyticsService] Comparative analysis retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[AnalyticsService] Failed to get comparative analysis:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[AnalyticsService] Error getting comparative analysis:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 记录分析事件（用于改进算法）
   */
  async recordAnalyticsEvent(
    userId: string,
    eventType: string,
    eventData: any
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('[AnalyticsService] Recording analytics event:', { userId, eventType });

      const result = await this.trpcClient.analytics.recordEvent.mutate({
        userId,
        eventType,
        eventData,
        timestamp: new Date().toISOString()
      });

      if (result.success) {
        console.log('[AnalyticsService] Analytics event recorded successfully');
        return { success: true };
      } else {
        console.warn('[AnalyticsService] Failed to record analytics event:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[AnalyticsService] Error recording analytics event:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
}

export default AnalyticsService;
