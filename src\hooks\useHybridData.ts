/**
 * 混合数据获取钩子
 * 根据离线在线实现计划，优先从服务端获取，在线不可用时使用离线数据
 */

import { trpc } from '@/lib/trpc';
import { Services } from '@/services';
import { useCallback, useEffect, useState } from 'react';

// 类型安全的tRPC调用包装器
const safeTrpcCall = async (tableName: string) => {
  try {
    // 使用类型断言来避免TypeScript类型检查问题
    const result = await (trpc as any).fetchTable.query({ tableName });
    return result;
  } catch (error) {
    throw new Error(
      `Failed to fetch ${tableName} from server: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};

interface UseHybridDataOptions {
  enableOnlineFirst?: boolean; // 是否优先使用在线服务
  enableOfflineFallback?: boolean; // 是否启用离线回退
  enableAutoSync?: boolean; // 是否启用自动同步
  syncInterval?: number; // 同步间隔（毫秒）
}

interface UseHybridDataReturn<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
  isOnline: boolean;
  lastSyncTime: Date | null;
  refresh: () => Promise<void>;
  forceSync: () => Promise<void>;
}

/**
 * 混合数据获取钩子
 * 实现在线优先、离线回退的数据获取策略
 */
export function useHybridData<T>(
  dataKey: string,
  fetchOnline: () => Promise<T>,
  fetchOffline: () => Promise<T>,
  options: UseHybridDataOptions = {}
): UseHybridDataReturn<T> {
  const {
    enableOnlineFirst = true,
    enableOfflineFallback = true,
    enableAutoSync = false,
    syncInterval = 5 * 60 * 1000, // 5分钟
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

  // 监听网络状态变化
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // 数据获取逻辑
  const fetchData = useCallback(
    async (forceOnline = false) => {
      try {
        setIsLoading(true);
        setError(null);

        let result: T | null = null;
        let dataSource = 'unknown';

        // 策略1: 在线优先
        if ((enableOnlineFirst && isOnline) || forceOnline) {
          try {
            console.log(`[useHybridData] Attempting to fetch ${dataKey} from online service...`);
            result = await fetchOnline();
            dataSource = 'online';
            setLastSyncTime(new Date());
            console.log(`[useHybridData] Successfully fetched ${dataKey} from online service`);
          } catch (onlineError) {
            console.warn(`[useHybridData] Online fetch failed for ${dataKey}:`, onlineError);

            // 策略2: 离线回退
            if (enableOfflineFallback) {
              try {
                console.log(`[useHybridData] Falling back to offline service for ${dataKey}...`);
                result = await fetchOffline();
                dataSource = 'offline';
                console.log(`[useHybridData] Successfully fetched ${dataKey} from offline service`);
              } catch (offlineError) {
                console.error(
                  `[useHybridData] Offline fetch also failed for ${dataKey}:`,
                  offlineError
                );
                throw new Error(`Both online and offline fetch failed: ${onlineError.message}`);
              }
            } else {
              throw onlineError;
            }
          }
        } else {
          // 策略3: 仅离线
          console.log(
            `[useHybridData] Fetching ${dataKey} from offline service (online disabled or unavailable)...`
          );
          result = await fetchOffline();
          dataSource = 'offline';
          console.log(`[useHybridData] Successfully fetched ${dataKey} from offline service`);
        }

        setData(result);
        console.log(`[useHybridData] Data loaded for ${dataKey} from ${dataSource} source`);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : `Failed to load ${dataKey}`;
        setError(new Error(errorMessage));
        console.error(`[useHybridData] Error loading ${dataKey}:`, err);
      } finally {
        setIsLoading(false);
      }
    },
    [dataKey, fetchOnline, fetchOffline, enableOnlineFirst, enableOfflineFallback, isOnline]
  );

  // 强制同步（仅在线）
  const forceSync = useCallback(async () => {
    if (!isOnline) {
      throw new Error('Cannot sync while offline');
    }
    await fetchData(true);
  }, [fetchData, isOnline]);

  // 刷新数据
  const refresh = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  // 初始数据加载
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 自动同步
  useEffect(() => {
    if (!enableAutoSync || !isOnline) {
      return;
    }

    const interval = setInterval(() => {
      console.log(`[useHybridData] Auto-syncing ${dataKey}...`);
      fetchData(true).catch((err) => {
        console.warn(`[useHybridData] Auto-sync failed for ${dataKey}:`, err);
      });
    }, syncInterval);

    return () => clearInterval(interval);
  }, [enableAutoSync, isOnline, syncInterval, fetchData, dataKey]);

  // 网络恢复时自动同步
  useEffect(() => {
    if (isOnline && enableOnlineFirst && data) {
      console.log(`[useHybridData] Network restored, syncing ${dataKey}...`);
      fetchData(true).catch((err) => {
        console.warn(`[useHybridData] Network restore sync failed for ${dataKey}:`, err);
      });
    }
  }, [isOnline, enableOnlineFirst, data, fetchData, dataKey]);

  return {
    data,
    isLoading,
    error,
    isOnline,
    lastSyncTime,
    refresh,
    forceSync,
  };
}

/**
 * 标签数据的混合获取钩子
 */
export function useHybridTagsData() {
  return useHybridData(
    'tags',
    async () => {
      // 直接使用tRPC获取在线标签数据
      const result = await safeTrpcCall('tags');
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch tags from server');
      }
      return result.data;
    },
    async () => {
      const tagService = await Services.tag();
      const result = await tagService.getAll();
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.data;
    },
    {
      enableOnlineFirst: true, // 启用在线服务
      enableOfflineFallback: true,
      enableAutoSync: true,
      syncInterval: 10 * 60 * 1000, // 10分钟同步一次
    }
  );
}

/**
 * 心情记录数据的混合获取钩子
 * 已迁移到使用 MoodTrackingService
 */
export function useHybridMoodEntriesData() {
  return useHybridData(
    'moodEntries',
    async () => {
      // 直接使用tRPC获取在线心情记录
      const result = await safeTrpcCall('mood_entries');
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch mood entries from server');
      }
      return result.data;
    },
    async () => {
      // ✅ 使用新的 MoodTrackingService
      const moodTrackingService = await Services.moodTracking();
      const result = await moodTrackingService.getAllEntries();
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.data;
    },
    {
      enableOnlineFirst: true, // 启用在线优先
      enableOfflineFallback: true,
      enableAutoSync: true, // 启用自动同步
      syncInterval: 5 * 60 * 1000, // 5分钟同步一次
    }
  );
}

/**
 * 情绪数据集的混合获取钩子
 * 已迁移到使用 QuizPackService，过滤情绪类别
 */
export function useHybridEmotionDataSetsData() {
  return useHybridData(
    'emotionDataSets',
    async () => {
      // 直接使用tRPC获取在线Quiz包数据，过滤情绪类别
      const result = await safeTrpcCall('quiz_packs');
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch quiz packs from server');
      }
      // 过滤情绪相关的包
      const emotionPacks = result.data.filter((pack: any) => pack.category === 'emotion');
      return emotionPacks;
    },
    async () => {
      // ✅ 使用新的 QuizPackService，过滤情绪类别
      const quizPackService = await Services.quizPack();
      const result = await quizPackService.getPacksByCategory('emotion');
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.data;
    },
    {
      enableOnlineFirst: true, // 启用在线服务
      enableOfflineFallback: true,
      enableAutoSync: true,
      syncInterval: 15 * 60 * 1000, // 15分钟同步一次
    }
  );
}

/**
 * 表情集数据的混合获取钩子
 * 使用 EmojiMappingService 获取表情符号映射数据
 */
export function useHybridEmojiSetsData() {
  return useHybridData(
    'emojiSets',
    async () => {
      // 直接使用tRPC获取在线表情集
      const result = await safeTrpcCall('emoji_sets');
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch emoji sets from server');
      }
      return result.data;
    },
    async () => {
      // ✅ 使用 EmojiMappingService 获取表情符号映射
      const emojiMappingService = await Services.emojiMapping();
      const result = await emojiMappingService.getAllEmojiSets();
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.data;
    },
    {
      enableOnlineFirst: true, // 启用在线服务
      enableOfflineFallback: true,
      enableAutoSync: true,
      syncInterval: 20 * 60 * 1000, // 20分钟同步一次
    }
  );
}

/**
 * 皮肤数据的混合获取钩子
 */
export function useHybridSkinsData() {
  return useHybridData(
    'skins',
    async () => {
      // 直接使用tRPC获取在线皮肤数据
      const result = await safeTrpcCall('skins');
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch skins from server');
      }
      return result.data;
    },
    async () => {
      const skinService = await Services.skin();
      const result = await skinService.getAll();
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.data;
    },
    {
      enableOnlineFirst: true, // 启用在线服务
      enableOfflineFallback: true,
      enableAutoSync: true,
      syncInterval: 30 * 60 * 1000, // 30分钟同步一次
    }
  );
}
