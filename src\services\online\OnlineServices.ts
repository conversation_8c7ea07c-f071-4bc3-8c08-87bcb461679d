/**
 * 在线服务管理器 (简化版)
 * 管理基础在线服务，业务逻辑通过tRPC直接调用服务端
 *
 * 注意：认证、用户管理、支付等业务逻辑现在通过tRPC直接调用服务端
 * 这个类只保留基础的网络和API客户端服务
 */

import { trpc } from '@/lib/trpc';
import { ApiClientService } from './ApiClientService';
import { NetworkStatusService } from './NetworkStatusService';
import { PaymentService } from './services/PaymentService';
import { ConfigService } from './services/ConfigService';
import { AnalyticsService } from './services/AnalyticsService';
import { ShopService } from './services/ShopService';
import type { OnlineServiceConfig } from './types/OnlineServiceTypes';

/**
 * 在线服务管理器 (简化版)
 * 只管理基础服务：网络状态和API客户端
 * 业务逻辑通过tRPC客户端直接调用服务端
 */
export class OnlineServices {
  private static instance: OnlineServices;
  private static isInitialized = false;
  private static config: OnlineServiceConfig | null = null;

  // 服务实例缓存
  private apiClientService?: ApiClientService;
  private networkStatusService?: NetworkStatusService;
  private paymentService?: PaymentService;
  private configService?: ConfigService;
  private analyticsService?: AnalyticsService;
  private shopService?: ShopService;

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): OnlineServices {
    if (!OnlineServices.instance) {
      OnlineServices.instance = new OnlineServices();
    }
    return OnlineServices.instance;
  }

  /**
   * 初始化在线服务
   */
  static async initialize(config?: Partial<OnlineServiceConfig>): Promise<void> {
    if (OnlineServices.isInitialized) {
      return;
    }

    try {
      // 设置默认配置
      const defaultConfig: OnlineServiceConfig = {
        baseUrl: 'http://localhost:8788', // 默认tRPC服务端地址
        apiKey: undefined,
        timeout: 10000,
        retryAttempts: 3,
        retryDelay: 1000,
        authConfig: {
          tokenStorageKey: 'mindful_mood_auth_token',
          autoRefresh: true,
          refreshThreshold: 5, // 5分钟前刷新
        },
      };

      // 合并用户配置
      OnlineServices.config = { ...defaultConfig, ...config };

      console.log('[OnlineServices] Initialized with config:', {
        baseUrl: OnlineServices.config.baseUrl,
        timeout: OnlineServices.config.timeout,
        retryAttempts: OnlineServices.config.retryAttempts,
      });

      OnlineServices.isInitialized = true;
    } catch (error) {
      console.error('[OnlineServices] Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * 检查是否已初始化
   */
  static isReady(): boolean {
    return OnlineServices.isInitialized;
  }

  /**
   * 获取API客户端服务
   */
  async apiClient(): Promise<ApiClientService> {
    if (!OnlineServices.isInitialized) {
      await OnlineServices.initialize();
    }

    if (!this.apiClientService) {
      this.apiClientService = ApiClientService.getInstance(OnlineServices.config || undefined);
    }

    return this.apiClientService;
  }

  /**
   * 获取网络状态服务
   */
  async networkStatus(): Promise<NetworkStatusService> {
    if (!OnlineServices.isInitialized) {
      await OnlineServices.initialize();
    }

    if (!this.networkStatusService) {
      this.networkStatusService = NetworkStatusService.getInstance();
    }

    return this.networkStatusService;
  }

  /**
   * 获取支付服务 (复杂业务服务)
   * 处理VIP订阅、皮肤购买等复杂支付流程
   */
  async payment(): Promise<PaymentService> {
    if (!OnlineServices.isInitialized) {
      await OnlineServices.initialize();
    }

    if (!this.paymentService) {
      this.paymentService = new PaymentService(trpc);
    }

    return this.paymentService;
  }

  /**
   * 获取配置服务
   * 处理Quiz配置、表情符号映射等配置相关的在线操作
   */
  async config(): Promise<ConfigService> {
    if (!OnlineServices.isInitialized) {
      await OnlineServices.initialize();
    }

    if (!this.configService) {
      this.configService = new ConfigService(trpc);
    }

    return this.configService;
  }

  /**
   * 获取分析服务
   * 处理用户数据分析、趋势分析等服务器端聚合操作
   */
  async analytics(): Promise<AnalyticsService> {
    if (!OnlineServices.isInitialized) {
      await OnlineServices.initialize();
    }

    if (!this.analyticsService) {
      this.analyticsService = new AnalyticsService(trpc);
    }

    return this.analyticsService;
  }

  /**
   * 获取商店服务
   * 处理皮肤、表情符号集、VIP订阅等商品的统一管理
   */
  async shop(): Promise<ShopService> {
    if (!OnlineServices.isInitialized) {
      await OnlineServices.initialize();
    }

    if (!this.shopService) {
      this.shopService = new ShopService(trpc);
    }

    return this.shopService;
  }

  /**
   * 获取当前配置
   */
  static getConfig(): OnlineServiceConfig | null {
    return OnlineServices.config;
  }

  /**
   * 检查网络连接状态
   */
  isOnline(): boolean {
    return navigator.onLine;
  }

  /**
   * 清理所有服务实例
   */
  clearCache(): void {
    this.apiClientService = undefined;
    this.networkStatusService = undefined;
    this.paymentService = undefined;
    this.configService = undefined;
    this.analyticsService = undefined;
    this.shopService = undefined;
    console.log('[OnlineServices] Service cache cleared');
  }

  /**
   * 重新配置在线服务
   */
  static async reconfigure(config: Partial<OnlineServiceConfig>): Promise<void> {
    OnlineServices.isInitialized = false;
    OnlineServices.getInstance().clearCache();
    await OnlineServices.initialize(config);
  }

  /**
   * 销毁服务管理器
   */
  static destroy(): void {
    if (OnlineServices.instance) {
      OnlineServices.instance.clearCache();
      OnlineServices.instance = null as any;
    }
    OnlineServices.isInitialized = false;
    OnlineServices.config = null;
    console.log('[OnlineServices] Service manager destroyed');
  }

  /**
   * 获取服务状态信息
   */
  getStatus(): {
    isInitialized: boolean;
    isOnline: boolean;
    config: OnlineServiceConfig | null;
  } {
    return {
      isInitialized: OnlineServices.isInitialized,
      isOnline: navigator.onLine,
      config: OnlineServices.config,
    };
  }
}

// 导出单例实例
export const onlineServices = OnlineServices.getInstance();

// 导出默认实例
export default onlineServices;
