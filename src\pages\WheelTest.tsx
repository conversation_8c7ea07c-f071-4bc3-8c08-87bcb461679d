// 使用新的 DisplayAdapter 替代废弃的组件
import DisplayAdapter from '@/components/core/DisplayAdapter';
import ColorModePreview from '@/components/settings/ColorModePreview';
import { Card } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useColorMode } from '@/contexts/ColorModeContext';
import type { Emotion, ContentDisplayMode, RenderEngine, ViewType } from '@/types';
import type React from 'react';
import { useState } from 'react';

/**
 * 轮盘测试页面
 * 用于测试轮盘扇区生成和颜色分配
 */
const WheelTest: React.FC = () => {
  const { colorMode, setColorMode } = useColorMode();

  // 测试数据
  const [emotionCount, setEmotionCount] = useState<number>(6);
  const [tierLevel, setTierLevel] = useState<number>(1);
  const [viewType, setViewType] = useState<ViewType>('wheel');
  const [renderEngine, setRenderEngine] = useState<RenderEngine>('D3');
  const [displayMode, setDisplayMode] = useState<ContentDisplayMode>('textEmoji');

  // 生成测试情绪数据
  const generateEmotions = (count: number): Emotion[] => {
    const emotions: Emotion[] = [];
    for (let i = 0; i < count; i++) {
      emotions.push({
        id: `emotion-${i}`,
        name: `情绪 ${i + 1}`,
        emoji: '😀',
        color: `#${Math.floor(Math.random() * 16777215).toString(16)}`, // 随机颜色
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_deleted: false,
      });
    }
    return emotions;
  };

  const testEmotions = generateEmotions(emotionCount);

  // 处理情绪选择
  const handleEmotionSelect = (emotion: Emotion) => {
    console.log('选择了情绪:', emotion);
  };

  // 处理颜色模式变更
  const handleColorModeChange = async (value: string) => {
    try {
      await setColorMode(value as 'warm' | 'cool' | 'mixed' | 'game');
    } catch (error) {
      console.error('Failed to change color mode:', error);
    }
  };

  // 处理视图类型变更
  const handleViewTypeChange = (value: string) => {
    setViewType(value as ViewType);
  };

  // 处理渲染引擎变更
  const handleRenderEngineChange = (value: string) => {
    setRenderEngine(value as RenderEngine);
  };

  // 处理显示模式变更
  const handleDisplayModeChange = (value: string) => {
    setDisplayMode(value as ContentDisplayMode);
  };

  // 处理情绪数量变更
  const handleEmotionCountChange = (value: string) => {
    setEmotionCount(Number.parseInt(value, 10));
  };

  // 处理层级变更
  const handleTierChange = (value: string) => {
    setTierLevel(Number.parseInt(value, 10));
  };

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">轮盘测试</h1>

      <div className="mb-6">
        <Card className="p-4">
          <h2 className="text-xl font-semibold mb-4">测试设置</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block mb-2">颜色模式</label>
              <Select value={colorMode} onValueChange={handleColorModeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择颜色模式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="warm">暖色系</SelectItem>
                  <SelectItem value="cool">冷色系</SelectItem>
                  <SelectItem value="mixed">混合色系</SelectItem>
                  <SelectItem value="game">游戏风格</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex gap-4 mt-2">
                {(['warm', 'cool', 'mixed', 'game'] as const).map((mode) => (
                  <div
                    key={mode}
                    className={`p-2 rounded-md cursor-pointer ${colorMode === mode ? 'bg-muted' : ''}`}
                    onClick={() => handleColorModeChange(mode)}
                  >
                    <ColorModePreview colorMode={mode} showLabel size="sm" />
                  </div>
                ))}
              </div>
            </div>

            <div>
              <label className="block mb-2">视图类型</label>
              <Select value={viewType} onValueChange={handleViewTypeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择视图类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="wheel">轮盘</SelectItem>
                  <SelectItem value="card">卡片</SelectItem>
                  <SelectItem value="bubble">气泡</SelectItem>
                  <SelectItem value="galaxy">星系</SelectItem>
                  <SelectItem value="grid">网格</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block mb-2">渲染引擎</label>
              <Select value={renderEngine} onValueChange={handleRenderEngineChange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择渲染引擎" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="D3">D3</SelectItem>
                  <SelectItem value="SVG">SVG</SelectItem>
                  <SelectItem value="R3F">R3F</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block mb-2">显示模式</label>
              <Select value={displayMode} onValueChange={handleDisplayModeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择显示模式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="text">仅文本</SelectItem>
                  <SelectItem value="emoji">仅表情</SelectItem>
                  <SelectItem value="textEmoji">文本和表情</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block mb-2">情绪数量</label>
              <Select value={emotionCount.toString()} onValueChange={handleEmotionCountChange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择情绪数量" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1个</SelectItem>
                  <SelectItem value="2">2个</SelectItem>
                  <SelectItem value="3">3个</SelectItem>
                  <SelectItem value="4">4个</SelectItem>
                  <SelectItem value="6">6个</SelectItem>
                  <SelectItem value="8">8个</SelectItem>
                  <SelectItem value="12">12个</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block mb-2">情绪层级</label>
              <Select value={tierLevel.toString()} onValueChange={handleTierChange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择情绪层级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">一级情绪</SelectItem>
                  <SelectItem value="2">二级情绪</SelectItem>
                  <SelectItem value="3">三级情绪</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </Card>
      </div>

      <div className="mb-6">
        <Card className="p-4">
          <h2 className="text-xl font-semibold mb-4">轮盘预览</h2>

          <Tabs defaultValue="wheel">
            <TabsList className="mb-4">
              <TabsTrigger value="wheel">轮盘视图</TabsTrigger>
              <TabsTrigger value="card">卡片视图</TabsTrigger>
              <TabsTrigger value="bubble">气泡视图</TabsTrigger>
              <TabsTrigger value="galaxy">星系视图</TabsTrigger>
            </TabsList>

            <TabsContent value="wheel" className="flex justify-center">
              <div className="w-full max-w-md">
                <DisplayAdapter
                  emotions={testEmotions}
                  tier={tierLevel}
                  onSelect={handleEmotionSelect}
                  displayOptions={{
                    viewType: 'wheel',
                    renderEngine: renderEngine,
                    displayMode: displayMode,
                  }}
                />
              </div>
            </TabsContent>

            <TabsContent value="card" className="flex justify-center">
              <div className="w-full max-w-md">
                <DisplayAdapter
                  emotions={testEmotions}
                  tier={tierLevel}
                  onSelect={handleEmotionSelect}
                  displayOptions={{
                    viewType: 'card',
                    renderEngine: 'D3',
                    displayMode: displayMode,
                  }}
                />
              </div>
            </TabsContent>

            <TabsContent value="bubble" className="flex justify-center">
              <div className="w-full max-w-md">
                <DisplayAdapter
                  emotions={testEmotions}
                  tier={tierLevel}
                  onSelect={handleEmotionSelect}
                  displayOptions={{
                    viewType: 'bubble',
                    renderEngine: 'D3',
                    displayMode: displayMode,
                  }}
                />
              </div>
            </TabsContent>

            <TabsContent value="galaxy" className="flex justify-center">
              <div className="w-full max-w-md">
                <DisplayAdapter
                  emotions={testEmotions}
                  tier={tierLevel}
                  onSelect={handleEmotionSelect}
                  displayOptions={{
                    viewType: 'galaxy',
                    renderEngine: 'D3',
                    displayMode: 'textEmoji',
                  }}
                />
              </div>
            </TabsContent>
          </Tabs>
        </Card>
      </div>

      <div className="mb-6">
        <Card className="p-4">
          <h2 className="text-xl font-semibold mb-4">测试数据</h2>
          <pre className="bg-muted p-4 rounded-md overflow-auto max-h-60">
            {JSON.stringify(testEmotions, null, 2)}
          </pre>
        </Card>
      </div>
    </div>
  );
};

export default WheelTest;
