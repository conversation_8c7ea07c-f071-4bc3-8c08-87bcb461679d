/**
 * 表情集管理页面
 * 用于创建、编辑和删除自定义表情集
 */

import EmojiDisplay from '@/components/emoji/EmojiDisplay';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLanguage } from '@/contexts/LanguageContext';
import { Services } from '@/services';
import {
  type CreateEmojiSetInput,
  EmojiItem,
  type EmojiSet,
  type EmojiSetType,
  type UpdateEmojiSetInput,
} from '@/types';
import { ArrowLeft, Download, Edit, Plus, RefreshCw, Save, Trash, Upload, X } from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';

/**
 * 表情集管理页面
 */
export default function EmojiSetManager() {
  const { t } = useLanguage();

  // 新的状态管理 - 使用服务层
  const [emojiSets, setEmojiSets] = useState<EmojiSet[]>([]);
  const [activeEmojiSet, setActiveEmojiSet] = useState<EmojiSet | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [activeTab, setActiveTab] = useState('list');
  const [selectedEmojiSet, setSelectedEmojiSet] = useState<EmojiSet | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  // 新建表情集的表单状态
  const [newEmojiSet, setNewEmojiSet] = useState<Partial<CreateEmojiSetInput>>({
    name: '',
    description: '',
    type: 'unicode',
    is_default: false,
    is_system: false,
    is_unlocked: true,
  });

  // 编辑表情集的表单状态
  const [editingEmojiSet, setEditingEmojiSet] = useState<Partial<UpdateEmojiSetInput>>({});

  // 数据加载 Effect
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 加载所有表情集 - 修复服务调用方式
        const emojiSetService = await Services.emojiSet();
        const emojiSetsResult = await emojiSetService.getAll();
        if (emojiSetsResult.success) {
          setEmojiSets(emojiSetsResult.data);
        }

        // 获取活动的表情集
        const activeEmojiSetsResult = await emojiSetService.getActiveEmojiSets();
        if (activeEmojiSetsResult.success && activeEmojiSetsResult.data.length > 0) {
          setActiveEmojiSet(activeEmojiSetsResult.data[0]);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load emoji sets';
        setError(errorMessage);
        console.error('Error loading emoji sets:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // 处理创建表情集
  const handleCreateEmojiSet = async () => {
    if (!newEmojiSet.name) {
      toast.error(t('emoji_set_manager.name_required', { fallback: '请输入表情集名称' }));
      return;
    }

    try {
      const createInput: CreateEmojiSetInput = {
        name: newEmojiSet.name,
        description: newEmojiSet.description,
        type: newEmojiSet.type || 'unicode',
        is_default: newEmojiSet.is_default || false,
        is_system: false,
        is_unlocked: newEmojiSet.is_unlocked !== false,
        price: newEmojiSet.price,
        created_by: 'user',
      };

      const emojiSetService = await Services.emojiSet();
      const result = await emojiSetService.create(createInput);
      if (result.success) {
        toast.success(t('emoji_set_manager.create_success', { fallback: '创建表情集成功' }));
        setIsCreateDialogOpen(false);

        // 重置表单
        setNewEmojiSet({
          name: '',
          description: '',
          type: 'unicode',
          is_default: false,
          is_system: false,
          is_unlocked: true,
        });

        // 刷新表情集列表
        const emojiSetsResult = await emojiSetService.getAll();
        if (emojiSetsResult.success) {
          setEmojiSets(emojiSetsResult.data);
        }
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Create failed';
      console.error('Failed to create emoji set:', error);
      toast.error(
        `${t('emoji_set_manager.create_error', { fallback: '创建表情集失败' })}: ${errorMessage}`
      );
    }
  };

  // 处理更新表情集
  const handleUpdateEmojiSet = async () => {
    if (!selectedEmojiSet || !editingEmojiSet.name) {
      toast.error(t('emoji_set_manager.name_required', { fallback: '请输入表情集名称' }));
      return;
    }

    try {
      const emojiSetService = await Services.emojiSet();
      const result = await emojiSetService.update(selectedEmojiSet.id, editingEmojiSet);
      if (result.success) {
        toast.success(t('emoji_set_manager.update_success', { fallback: '更新表情集成功' }));
        setIsEditDialogOpen(false);

        // 刷新表情集列表
        const emojiSetsResult = await emojiSetService.getAll();
        if (emojiSetsResult.success) {
          setEmojiSets(emojiSetsResult.data);
        }
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Update failed';
      console.error('Failed to update emoji set:', error);
      toast.error(
        `${t('emoji_set_manager.update_error', { fallback: '更新表情集失败' })}: ${errorMessage}`
      );
    }
  };

  // 处理删除表情集
  const handleDeleteEmojiSet = async () => {
    if (!selectedEmojiSet) return;

    try {
      const emojiSetService = await Services.emojiSet();
      const result = await emojiSetService.delete(selectedEmojiSet.id);
      if (result.success) {
        toast.success(t('emoji_set_manager.delete_success', { fallback: '删除表情集成功' }));
        setIsDeleteDialogOpen(false);
        setSelectedEmojiSet(null);

        // 刷新表情集列表
        const emojiSetsResult = await emojiSetService.getAll();
        if (emojiSetsResult.success) {
          setEmojiSets(emojiSetsResult.data);
        }
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Delete failed';
      console.error('Failed to delete emoji set:', error);
      toast.error(
        `${t('emoji_set_manager.delete_error', { fallback: '删除表情集失败' })}: ${errorMessage}`
      );
    }
  };

  // 处理编辑表情集
  const handleEditEmojiSet = (emojiSet: EmojiSet) => {
    setSelectedEmojiSet(emojiSet);
    setEditingEmojiSet({
      name: emojiSet.name,
      description: emojiSet.description,
      type: emojiSet.type || 'unicode',
      is_default: emojiSet.is_default,
      is_unlocked: emojiSet.is_unlocked,
    });
    setIsEditDialogOpen(true);
  };

  // 处理激活表情集
  const handleActivateEmojiSet = async (emojiSetId: string) => {
    try {
      const emojiSetService = await Services.emojiSet();
      const result = await emojiSetService.setActiveEmojiSet(emojiSetId);
      if (result.success) {
        // 更新本地状态
        const selectedSet = emojiSets.find((set) => set.id === emojiSetId);
        if (selectedSet) {
          setActiveEmojiSet(selectedSet);
        }
        toast.success(t('emoji_set_manager.activate_success', { fallback: '激活表情集成功' }));
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Activation failed';
      console.error('Failed to activate emoji set:', error);
      toast.error(
        `${t('emoji_set_manager.activate_error', { fallback: '激活表情集失败' })}: ${errorMessage}`
      );
    }
  };

  // 处理删除表情集对话框
  const handleDeleteEmojiSetDialog = (emojiSet: EmojiSet) => {
    setSelectedEmojiSet(emojiSet);
    setIsDeleteDialogOpen(true);
  };

  // 渲染表情集列表
  const renderEmojiSetList = () => {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">
            {t('emoji_set_manager.emoji_sets', { fallback: '表情集' })}
          </h2>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            {t('emoji_set_manager.create', { fallback: '创建' })}
          </Button>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>{t('common.loading', 'Loading...')}</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-destructive mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>{t('common.retry', 'Retry')}</Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {emojiSets.map((emojiSet: EmojiSet) => (
              <Card
                key={emojiSet.id}
                className={`overflow-hidden ${emojiSet.id === activeEmojiSet?.id ? 'border-primary' : ''}`}
              >
                <CardHeader className="p-4">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{emojiSet.name}</CardTitle>
                    <div className="flex gap-1">
                      {emojiSet.is_system && (
                        <div className="text-xs px-2 py-1 bg-blue-500/10 text-blue-500 rounded-full">
                          {t('emoji_set_manager.system', { fallback: '系统' })}
                        </div>
                      )}
                      {emojiSet.is_default && (
                        <div className="text-xs px-2 py-1 bg-green-500/10 text-green-500 rounded-full">
                          {t('emoji_set_manager.default', { fallback: '默认' })}
                        </div>
                      )}
                    </div>
                  </div>
                  <CardDescription>{emojiSet.description}</CardDescription>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="flex flex-wrap gap-2">
                    <div className="text-xs px-2 py-1 bg-secondary/10 text-secondary rounded-full">
                      {emojiSet.type || 'unicode'}
                    </div>
                    {emojiSet.items_count && (
                      <div className="text-xs px-2 py-1 bg-muted text-muted-foreground rounded-full">
                        {emojiSet.items_count} {t('emoji_set_manager.items', 'items')}
                      </div>
                    )}
                  </div>

                  <div className="mt-4 flex flex-wrap gap-2">
                    {/* 显示表情项数量信息 */}
                    {emojiSet.items && emojiSet.items.length > 0 && (
                      <div className="text-sm text-muted-foreground">
                        {t(
                          'emoji_set_manager.contains_items',
                          `Contains ${emojiSet.items.length} emoji items`
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="p-4 pt-0 flex justify-between">
                  <div className="flex gap-2">
                    {!emojiSet.is_system && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditEmojiSet(emojiSet)}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          {t('emoji_set_manager.edit', { fallback: '编辑' })}
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteEmojiSetDialog(emojiSet)}
                        >
                          <Trash className="h-4 w-4 mr-1" />
                          {t('emoji_set_manager.delete', { fallback: '删除' })}
                        </Button>
                      </>
                    )}
                  </div>
                  <Button
                    variant={emojiSet.id === activeEmojiSet?.id ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleActivateEmojiSet(emojiSet.id)}
                    disabled={emojiSet.id === activeEmojiSet?.id}
                  >
                    {emojiSet.id === activeEmojiSet?.id
                      ? t('emoji_set_manager.active', { fallback: '当前活动' })
                      : t('emoji_set_manager.set_active', { fallback: '设为活动' })}
                  </Button>
                </CardFooter>
              </Card>
            ))}
            {emojiSets.length === 0 && (
              <div className="col-span-full text-center py-8">
                <p className="text-muted-foreground">
                  {t('emoji_set_manager.no_emoji_sets', 'No emoji sets found')}
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="flex items-center gap-2 mb-6">
        <Link to="/settings">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">
          {t('emoji_set_manager.title', { fallback: '表情集管理' })}
        </h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="list">
            {t('emoji_set_manager.list', { fallback: '表情集列表' })}
          </TabsTrigger>
          <TabsTrigger value="import_export">
            {t('emoji_set_manager.import_export', { fallback: '导入/导出' })}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          {renderEmojiSetList()}
        </TabsContent>

        <TabsContent value="import_export" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {t('emoji_set_manager.import_export', { fallback: '导入/导出' })}
              </CardTitle>
              <CardDescription>
                {t('emoji_set_manager.import_export_desc', { fallback: '导入或导出表情集数据' })}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">
                  {t('emoji_set_manager.export', { fallback: '导出' })}
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  {t('emoji_set_manager.export_desc', { fallback: '导出所有自定义表情集数据' })}
                </p>
                <Button>
                  <Download className="h-4 w-4 mr-2" />
                  {t('emoji_set_manager.export_button', { fallback: '导出表情集' })}
                </Button>
              </div>

              <Separator />

              <div>
                <h3 className="text-lg font-medium mb-2">
                  {t('emoji_set_manager.import', { fallback: '导入' })}
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  {t('emoji_set_manager.import_desc', { fallback: '导入表情集数据' })}
                </p>
                <Button>
                  <Upload className="h-4 w-4 mr-2" />
                  {t('emoji_set_manager.import_button', { fallback: '导入表情集' })}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 创建表情集对话框 */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {t('emoji_set_manager.create_emoji_set', { fallback: '创建表情集' })}
            </DialogTitle>
            <DialogDescription>
              {t('emoji_set_manager.create_emoji_set_desc', {
                fallback: '创建一个新的自定义表情集',
              })}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="name">{t('emoji_set_manager.name', { fallback: '名称' })}</Label>
              <Input
                id="name"
                value={newEmojiSet.name}
                onChange={(e) => setNewEmojiSet({ ...newEmojiSet, name: e.target.value })}
                placeholder={t('emoji_set_manager.name_placeholder', {
                  fallback: '输入表情集名称',
                })}
              />
            </div>

            <div>
              <Label htmlFor="description">
                {t('emoji_set_manager.description', { fallback: '描述' })}
              </Label>
              <Input
                id="description"
                value={newEmojiSet.description}
                onChange={(e) => setNewEmojiSet({ ...newEmojiSet, description: e.target.value })}
                placeholder={t('emoji_set_manager.description_placeholder', {
                  fallback: '输入表情集描述',
                })}
              />
            </div>

            <div>
              <Label htmlFor="type">{t('emoji_set_manager.type', { fallback: '类型' })}</Label>
              <Select
                value={newEmojiSet.type as string}
                onValueChange={(value) =>
                  setNewEmojiSet({ ...newEmojiSet, type: value as EmojiSetType })
                }
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={t('emoji_set_manager.select_type', { fallback: '选择类型' })}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="unicode">
                    {t('emoji_set_manager.type_unicode', { fallback: 'Unicode 表情' })}
                  </SelectItem>
                  <SelectItem value="image">
                    {t('emoji_set_manager.type_image', { fallback: '图片表情' })}
                  </SelectItem>
                  <SelectItem value="svg">
                    {t('emoji_set_manager.type_svg', { fallback: 'SVG 表情' })}
                  </SelectItem>
                  <SelectItem value="animated">
                    {t('emoji_set_manager.type_animated', { fallback: '动画表情' })}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              {t('common.cancel', { fallback: '取消' })}
            </Button>
            <Button onClick={handleCreateEmojiSet}>
              {t('emoji_set_manager.create', { fallback: '创建' })}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑表情集对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {t('emoji_set_manager.edit_emoji_set', { fallback: '编辑表情集' })}
            </DialogTitle>
            <DialogDescription>
              {t('emoji_set_manager.edit_emoji_set_desc', { fallback: '编辑表情集信息' })}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-name">{t('emoji_set_manager.name', { fallback: '名称' })}</Label>
              <Input
                id="edit-name"
                value={editingEmojiSet.name}
                onChange={(e) => setEditingEmojiSet({ ...editingEmojiSet, name: e.target.value })}
                placeholder={t('emoji_set_manager.name_placeholder', {
                  fallback: '输入表情集名称',
                })}
              />
            </div>

            <div>
              <Label htmlFor="edit-description">
                {t('emoji_set_manager.description', { fallback: '描述' })}
              </Label>
              <Input
                id="edit-description"
                value={editingEmojiSet.description}
                onChange={(e) =>
                  setEditingEmojiSet({ ...editingEmojiSet, description: e.target.value })
                }
                placeholder={t('emoji_set_manager.description_placeholder', {
                  fallback: '输入表情集描述',
                })}
              />
            </div>

            <div>
              <Label htmlFor="edit-type">{t('emoji_set_manager.type', { fallback: '类型' })}</Label>
              <Select
                value={editingEmojiSet.type as string}
                onValueChange={(value) =>
                  setEditingEmojiSet({ ...editingEmojiSet, type: value as EmojiSetType })
                }
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={t('emoji_set_manager.select_type', { fallback: '选择类型' })}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="unicode">
                    {t('emoji_set_manager.type_unicode', { fallback: 'Unicode 表情' })}
                  </SelectItem>
                  <SelectItem value="image">
                    {t('emoji_set_manager.type_image', { fallback: '图片表情' })}
                  </SelectItem>
                  <SelectItem value="svg">
                    {t('emoji_set_manager.type_svg', { fallback: 'SVG 表情' })}
                  </SelectItem>
                  <SelectItem value="animated">
                    {t('emoji_set_manager.type_animated', { fallback: '动画表情' })}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              {t('common.cancel', { fallback: '取消' })}
            </Button>
            <Button onClick={handleUpdateEmojiSet}>{t('common.save', { fallback: '保存' })}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除表情集对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {t('emoji_set_manager.delete_emoji_set', { fallback: '删除表情集' })}
            </DialogTitle>
            <DialogDescription>
              {t('emoji_set_manager.delete_emoji_set_desc', {
                fallback: '确定要删除这个表情集吗？此操作不可撤销。',
              })}
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              {t('common.cancel', { fallback: '取消' })}
            </Button>
            <Button variant="destructive" onClick={handleDeleteEmojiSet}>
              {t('emoji_set_manager.delete', { fallback: '删除' })}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
