# 完整用户旅程测试计划

## 📋 测试目标

从用户首次打开应用到完成完整Quiz流程的端到端测试，验证：
1. 离线数据初始化流程
2. 统一Quiz架构的正确性
3. 默认Quiz配置的加载
4. 用户配置和偏好设置
5. Quiz会话管理和数据持久化

## 🗂️ 测试数据结构

### 1. 基础配置数据 (`public/seeds/config/`)
- ✅ `mood-track-quest-branching.sql` - 情绪Quiz包（50个问题，带分支）
- ✅ `tcm-quiz-packs.sql` - 中医Quiz包（19个量表）
- ✅ `ui_labels.sql` - UI标签和翻译
- ✅ `skin_configs.sql` - 皮肤配置
- ✅ `complete_emotions.sql` - 情绪数据

### 2. 测试用户数据 (`public/seeds/test-user-data/`)
- ✅ `users.sql` - 测试用户账户
- ✅ `user_preferences.sql` - 用户偏好设置
- ✅ `quiz_sessions_test.sql` - Quiz会话示例
- ✅ `quiz_answers_test.sql` - Quiz答案示例
- ✅ `mood_entries.sql` - 情绪记录示例
- ❌ `user_configs_test.sql` - **缺失：用户配置测试数据**
- ❌ `quiz_pack_overrides_test.sql` - **缺失：Quiz包覆盖配置**
- ❌ `user_presentation_configs_test.sql` - **缺失：用户展示配置**

## 🔄 离线数据初始化流程

### 阶段1: 数据库架构初始化
```sql
-- 1. 创建所有表结构
.read public/seeds/schema/full.sql

-- 2. 加载基础配置数据
.read public/seeds/config/master.sql
```

### 阶段2: 测试数据加载
```sql
-- 3. 加载测试用户数据
.read public/seeds/test-user-data/master.sql
```

### 阶段3: 应用启动验证
- DatabaseContext 初始化数据库连接
- ServiceFactory 设置数据库连接
- 各个Context加载初始数据

## 🎯 用户旅程测试场景

### 场景1: 首次启动用户
**目标**: 验证新用户的完整onboarding流程

**步骤**:
1. **应用启动**
   - [ ] 数据库成功初始化
   - [ ] 基础配置数据加载完成
   - [ ] 默认Quiz包正确识别（情绪Quiz）

2. **用户配置创建**
   - [ ] 自动创建默认用户配置
   - [ ] 语言设置为系统默认
   - [ ] 皮肤设置为默认主题

3. **首页数据加载**
   - [ ] 默认Quiz包显示（情绪Quiz，50个问题）
   - [ ] 推荐Quiz包列表加载
   - [ ] 用户统计数据初始化

### 场景2: 返回用户
**目标**: 验证已有用户的数据恢复和个性化设置

**步骤**:
1. **用户识别**
   - [ ] 从本地存储恢复用户ID
   - [ ] 加载用户配置和偏好

2. **个性化数据加载**
   - [ ] 恢复上次选择的Quiz包
   - [ ] 恢复语言和皮肤设置
   - [ ] 加载历史Quiz会话

### 场景3: Quiz执行流程
**目标**: 验证完整的Quiz执行和数据保存

**步骤**:
1. **Quiz启动**
   - [ ] 创建新的Quiz会话
   - [ ] 加载Quiz问题和选项
   - [ ] 初始化答案收集

2. **答题过程**
   - [ ] 逐题回答并保存
   - [ ] 支持分支逻辑（如果有）
   - [ ] 进度跟踪和状态保存

3. **Quiz完成**
   - [ ] 计算和保存结果
   - [ ] 更新用户统计
   - [ ] 生成情绪记录

### 场景4: 配置和设置
**目标**: 验证用户配置系统的完整性

**步骤**:
1. **全局设置**
   - [ ] 语言切换功能
   - [ ] 皮肤/主题切换
   - [ ] 通知设置

2. **Quiz设置**
   - [ ] 默认Quiz包选择
   - [ ] 个性化级别调整
   - [ ] 问题显示偏好

## 🧪 技术测试重点

### 1. 服务架构统一性
- [ ] 所有服务使用 `ServiceFactoryFixed`
- [ ] 数据库连接正确传递
- [ ] 方法调用签名一致

### 2. 数据一致性
- [ ] Quiz包数据结构标准化
- [ ] 问题和选项关联正确
- [ ] 用户数据关联完整

### 3. 离线功能
- [ ] 无网络环境下正常运行
- [ ] 本地数据持久化
- [ ] 数据同步机制（未来）

## 📊 测试验证点

### 数据加载验证
```sql
-- 验证Quiz包加载
SELECT COUNT(*) as quiz_packs FROM quiz_packs WHERE is_active = 1;

-- 验证默认Quiz包
SELECT * FROM quiz_packs WHERE category = 'emotion' AND is_default = 1;

-- 验证问题数量
SELECT pack_id, COUNT(*) as question_count 
FROM quiz_questions 
GROUP BY pack_id;
```

### 功能验证
- [ ] 首页正确显示默认Quiz
- [ ] Quiz设置页面无错误
- [ ] 语言切换生效
- [ ] 皮肤切换生效
- [ ] Quiz执行流程完整

## 🚨 已知问题和修复

### ✅ 已修复
1. **服务架构统一** - 所有Context使用ServiceFactoryFixed
2. **数据库连接** - DatabaseContext正确设置连接
3. **UILabel列问题** - 移除不存在的is_active列引用
4. **Quiz数据特殊化** - 移除emotion wheel特殊处理

### ❌ 待修复
1. **测试数据不完整** - 需要补充用户配置测试数据
2. **默认Quiz配置** - 需要在数据库中标记默认Quiz
3. **QuizTierNavigation组件** - 需要适配新的数据结构

## 📝 下一步行动

1. **补充测试数据** - 创建缺失的测试用户配置
2. **修复组件适配** - 更新QuizTierNavigation组件
3. **端到端测试** - 执行完整用户旅程测试
4. **性能优化** - 优化数据加载和渲染性能
