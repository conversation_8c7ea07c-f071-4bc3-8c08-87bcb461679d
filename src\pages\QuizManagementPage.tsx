/**
 * Quiz量表管理页面
 * 用于管理Quiz包、问题和问题选项
 * 支持CSV批量导入功能
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useLanguage } from '@/contexts/LanguageContext';
import { Services } from '@/services';
import { QuizPack, QuizQuestion, QuizQuestionOption } from '@/types/schema/base';
import { toast } from 'sonner';
import { Loading } from '@/components/ui/loading';
import { QuizPackManager } from '@/components/quiz/management/QuizPackManager';
import { QuizQuestionManager } from '@/components/quiz/management/QuizQuestionManager';
import { QuizOptionManager } from '@/components/quiz/management/QuizOptionManager';
import { CSVImporter } from '@/components/quiz/management/CSVImporter';
import './QuizManagementPage.css';

type ManagementTab = 'packs' | 'questions' | 'options' | 'import';

const QuizManagementPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useLanguage();

  // 状态管理
  const [activeTab, setActiveTab] = useState<ManagementTab>('packs');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 数据状态
  const [quizPacks, setQuizPacks] = useState<QuizPack[]>([]);
  const [selectedPack, setSelectedPack] = useState<QuizPack | null>(null);
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [selectedQuestion, setSelectedQuestion] = useState<QuizQuestion | null>(null);
  const [options, setOptions] = useState<QuizQuestionOption[]>([]);

  // 初始化数据加载
  useEffect(() => {
    loadInitialData();
  }, []);

  // 当选择特定包时，加载其问题
  useEffect(() => {
    if (selectedPack) {
      loadQuestions(selectedPack.id);
    }
  }, [selectedPack]);

  // 当选择特定问题时，加载其选项
  useEffect(() => {
    if (selectedQuestion) {
      loadOptions(selectedQuestion.id);
    }
  }, [selectedQuestion]);

  // 如果URL中有ID，自动选择对应的包
  useEffect(() => {
    if (id && quizPacks.length > 0) {
      const pack = quizPacks.find(p => p.id === id);
      if (pack) {
        setSelectedPack(pack);
        setActiveTab('questions');
      }
    }
  }, [id, quizPacks]);

  /**
   * 加载初始数据
   */
  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const quizPackService = await Services.quizPack();
      const result = await quizPackService.getAll();
      
      if (result.success) {
        setQuizPacks(result.data);
      } else {
        throw new Error(result.error || 'Failed to load quiz packs');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load data';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 加载指定包的问题
   */
  const loadQuestions = async (packId: string) => {
    try {
      const quizQuestionService = await Services.quizQuestion();
      const result = await quizQuestionService.getQuizPackQuestions(packId);
      
      if (result.success) {
        setQuestions(result.data);
      } else {
        toast.error('Failed to load questions: ' + result.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load questions';
      toast.error(errorMessage);
    }
  };

  /**
   * 加载指定问题的选项
   */
  const loadOptions = async (questionId: string) => {
    try {
      const quizQuestionService = await Services.quizQuestion();
      const optionsResult = await quizQuestionService.getQuestionOptions(questionId);
      
      if (optionsResult.success) {
        setOptions(optionsResult.data);
      } else {
        toast.error('Failed to load options: ' + optionsResult.error);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load options';
      toast.error(errorMessage);
    }
  };

  /**
   * 处理标签切换
   */
  const handleTabChange = (tab: ManagementTab) => {
    setActiveTab(tab);
    
    // 根据标签重置相关状态
    switch (tab) {
      case 'packs':
        setSelectedPack(null);
        setSelectedQuestion(null);
        setQuestions([]);
        setOptions([]);
        break;
      case 'questions':
        setSelectedQuestion(null);
        setOptions([]);
        break;
      case 'options':
        // 保持当前选择
        break;
      case 'import':
        // 导入页面不需要特殊处理
        break;
    }
  };

  /**
   * 处理包选择
   */
  const handlePackSelect = (pack: QuizPack) => {
    setSelectedPack(pack);
    setActiveTab('questions');
    navigate(`/quiz-management/${pack.id}`);
  };

  /**
   * 处理问题选择
   */
  const handleQuestionSelect = (question: QuizQuestion) => {
    setSelectedQuestion(question);
    setActiveTab('options');
  };

  /**
   * 处理CSV导入成功
   */
  const handleImportSuccess = () => {
    toast.success('CSV导入成功');
    loadInitialData(); // 重新加载数据
    setActiveTab('packs'); // 切换到包管理页面
  };

  /**
   * 处理数据更新
   */
  const handleDataUpdate = () => {
    // 根据当前标签重新加载相应数据
    switch (activeTab) {
      case 'packs':
        loadInitialData();
        break;
      case 'questions':
        if (selectedPack) {
          loadQuestions(selectedPack.id);
        }
        break;
      case 'options':
        if (selectedQuestion) {
          loadOptions(selectedQuestion.id);
        }
        break;
    }
  };

  /**
   * 渲染标签导航
   */
  const renderTabNavigation = () => (
    <div className="quiz-management-tabs">
      <button
        className={`tab-button ${activeTab === 'packs' ? 'active' : ''}`}
        onClick={() => handleTabChange('packs')}
      >
        <span className="tab-icon">📦</span>
        Quiz包管理
      </button>
      <button
        className={`tab-button ${activeTab === 'questions' ? 'active' : ''}`}
        onClick={() => handleTabChange('questions')}
        disabled={!selectedPack}
      >
        <span className="tab-icon">❓</span>
        问题管理
        {selectedPack && <span className="tab-badge">{questions.length}</span>}
      </button>
      <button
        className={`tab-button ${activeTab === 'options' ? 'active' : ''}`}
        onClick={() => handleTabChange('options')}
        disabled={!selectedQuestion}
      >
        <span className="tab-icon">📝</span>
        选项管理
        {selectedQuestion && <span className="tab-badge">{options.length}</span>}
      </button>
      <button
        className={`tab-button ${activeTab === 'import' ? 'active' : ''}`}
        onClick={() => handleTabChange('import')}
      >
        <span className="tab-icon">📤</span>
        CSV导入
      </button>
    </div>
  );

  /**
   * 渲染面包屑导航
   */
  const renderBreadcrumb = () => (
    <div className="quiz-management-breadcrumb">
      <span 
        className="breadcrumb-item clickable"
        onClick={() => handleTabChange('packs')}
      >
        Quiz包
      </span>
      {selectedPack && (
        <>
          <span className="breadcrumb-separator">›</span>
          <span 
            className="breadcrumb-item clickable"
            onClick={() => handleTabChange('questions')}
          >
            {selectedPack.name}
          </span>
        </>
      )}
      {selectedQuestion && (
        <>
          <span className="breadcrumb-separator">›</span>
          <span className="breadcrumb-item">
            {selectedQuestion.question_text}
          </span>
        </>
      )}
    </div>
  );

  /**
   * 渲染主要内容
   */
  const renderContent = () => {
    switch (activeTab) {
      case 'packs':
        return (
          <QuizPackManager
            quizPacks={quizPacks}
            onPackSelect={handlePackSelect}
            onDataUpdate={handleDataUpdate}
          />
        );
      
      case 'questions':
        return selectedPack ? (
          <QuizQuestionManager
            quizPack={selectedPack}
            questions={questions}
            onQuestionSelect={handleQuestionSelect}
            onDataUpdate={handleDataUpdate}
          />
        ) : (
          <div className="empty-state">
            <p>请先选择一个Quiz包</p>
          </div>
        );
      
      case 'options':
        return selectedQuestion ? (
          <QuizOptionManager
            question={selectedQuestion}
            options={options}
            onDataUpdate={handleDataUpdate}
          />
        ) : (
          <div className="empty-state">
            <p>请先选择一个问题</p>
          </div>
        );
      
      case 'import':
        return (
          <CSVImporter
            onImportSuccess={handleImportSuccess}
          />
        );
      
      default:
        return null;
    }
  };

  if (isLoading) {
    return <Loading />;
  }

  if (error) {
    return (
      <div className="quiz-management-error">
        <h2>加载错误</h2>
        <p>{error}</p>
        <button onClick={loadInitialData} className="retry-button">
          重试
        </button>
        <button onClick={() => navigate('/settings')} className="back-button">
          返回设置
        </button>
      </div>
    );
  }

  return (
    <div className="quiz-management-page">
      <div className="quiz-management-header">
        <h1>Quiz量表管理</h1>
        <p>管理Quiz包、问题和问题选项，支持CSV批量导入</p>
      </div>

      {renderBreadcrumb()}
      {renderTabNavigation()}

      <div className="quiz-management-content">
        {renderContent()}
      </div>
    </div>
  );
};

export default QuizManagementPage;
