### 页面迁移计划：应对量表与配置的滞后

**核心背景与目标：**

*   数据库架构已完成向"通用测验系统 V2"的迁移，强调数据与展现分离，并大量使用 JSON 字段实现配置的灵活性和细粒度控制。
*   `UserPresentationConfigs` 表中的 6 层 `QuizPresentationConfig` 是所有个性化设置的中心。
*   `QuizConfigMergerService` 负责在不同配置层（系统默认、测验包默认、用户偏好、测验包覆盖、问题覆盖）之间进行合并，生成最终的 `final_presentation_config`。
*   当前 `src/pages` 中某些 UI 元素可能仍依赖于旧的、已废弃的概念或未能充分利用新架构的灵活性，导致量表和配置方面的"滞后"。

**迁移计划的目标是：**

确保 `src/pages` 中的 UI 组件能够完全适配新的数据库架构和配置系统，提供细粒度的个性化控制，并正确展现和处理各种量表和配置。

---

#### 一、 核心配置管理页面：`src/pages/QuizSettings.tsx` 的迁移重点

`QuizSettings.tsx` 是用户个性化配置的核心入口，其迁移优先级最高。

**当前差距回顾：**

*   **粒度问题自定义持久化：** 尽管 `PersonalizationConfig` 定义了 `question_management.question_customization`，但 UI 与后端服务之间如何精确持久化这些深度嵌套的、每个测验包的问题自定义（启用/禁用、重新排序）仍需验证和增强。
*   **未能充分暴露所有 6 层配置的 UI 控件。**

**迁移步骤与具体行动：**

1.  **全面暴露 6 层 `QuizPresentationConfig`：**
    *   **设计和实现 UI 控件：** 为 `QuizSettings.tsx` 中的 `presentationConfig` 的所有 6 层（`layer0_dataset_presentation` 到 `layer5_accessibility`）设计并实现对应的 UI 控件。
    *   **重点关注：**
        *   `layer0_dataset_presentation` 中的 `question_management.question_customization`：开发 UI 允许用户在测验包级别对单个问题进行启用/禁用和拖放排序。确保这些更改能通过 `updatePresentationConfig` 正确映射到 JSON 字段并持久化。
        *   `layer4_view_detail.emotion_presentation`：引入 UI 允许用户为每个情绪（例如，"快乐"、"悲伤"）配置 `primary` 和 `alternatives` 表情符号、关联的颜色和动画效果。这将直接利用 `user_emoji_preferences.sql` 中测试数据的结构。
2.  **集成 `Pack Presentation Overrides` 管理：**
    *   **实现 UI：** 在 `QuizSettings.tsx` 中（或通过链接到专门的"测验包特定设置"子页面），提供 UI 允许用户为特定的测验包设置 `pack_presentation_overrides`，例如 `show_progress_bar`、`question_timeout`、`adaptive_difficulty` 等。
    *   **服务交互：** 确保这些覆盖能通过 `useQuizConfig` 或相关服务（需要对应的 tRPC 端点）进行读取、创建、更新和删除。
3.  **动态量表配置 UI：**
    *   **适配 `quiz_questions` 和 `quiz_question_options`：** 如果 `QuizSettings.tsx` 包含任何高级的测验/量表设计功能（例如，内容创建者模式），确保其 UI 能够动态地反映 `quiz_questions` 中定义的 `question_type`（特别是 `scale_rating`），并允许配置 `question_config` 和 `quiz_question_options` 中的 `scoring_value`、`min_value`、`max_value`、`step_value` 等字段。

---

#### 二、 表情符号管理页面：`src/pages/EmojiSetManager.tsx` 的迁移重点

`EmojiSetManager.tsx` 需要从单纯管理表情符号集，转向管理更细粒度的表情符号映射。

**当前差距回顾：**

*   页面侧重于管理 `emoji_sets`，但与 `config.emoji` tRPC 路由（用于管理选项/问题中的单个表情符号映射）的连接存在差距。
*   未能充分暴露 `user_presentation_configs` 和 `question_presentation_overrides` 中定义的细粒度映射能力。

**迁移步骤与具体行动：**

1.  **重构表情符号映射管理 UI：**
    *   **引入情绪-表情符号映射视图：** 在 `EmojiSetManager.tsx` 中（或作为其一个主要功能），引入一个视图，允许用户：
        *   选择情绪（例如，"快乐"、"悲伤"）。
        *   为该情绪设置"主要"表情符号。
        *   添加和管理"备用"表情符号。
        *   配置该情绪的关联颜色和动画。
    *   **与新架构对齐：** 确保这些 UI 控件直接操作 `user_presentation_configs.presentation_config.layer4_view_detail.emotion_presentation.emoji_mapping` JSON 字段。
2.  **集成 `config.emoji` tRPC 端点：**
    *   **数据持久化：** 确保通过 `EmojiSetManager.tsx` 进行的所有表情符号映射更改（包括全局用户偏好和问题特定覆盖）都通过 `config.emoji` tRPC 路由（特别是 `updateUserEmojiMapping` 和 `updateQuestionEmojiOverride`）进行持久化。
    *   **废弃概念清理：** 确保不再直接依赖 `emotions` 或 `emoji_items` 表来获取或存储表情符号与情绪的映射关系。

---

#### 三、 测验与量表展示页面：`src/pages/QuizSession.tsx` 和 `src/pages/WheelTest.tsx` 的迁移重点

这些页面负责实际渲染测验和量表，需要确保它们能够正确消费和应用个性化配置。

**当前差距回顾：**

*   **`QuizSession.tsx`：** 关键在于确保 `useQuizSessionData` 和底层的 `QuizEngineV3` 正确消费并应用 `final_presentation_config`。
*   **`WheelTest.tsx`：** 明确使用了 `useMockEmotionData()`，表明尚未与生产数据源和个性化表情符号映射对齐。

**迁移步骤与具体行动：**

1.  **`QuizSession.tsx` - 完全应用 `final_presentation_config`：**
    *   **强制配置传递：** 确保 `QuizLauncher.tsx` 在导航到 `QuizSession.tsx` 之前，强制调用 `config.quiz.generateSessionConfig` 并将返回的 `final_presentation_config` (存储在 `quiz_session_presentation_configs` 中) 作为参数或通过上下文传递给 `QuizSession.tsx`。
    *   **`QuizEngineV3` 的消费：** 验证 `QuizEngineV3` 及其内部组件能够完全解析并应用 `final_presentation_config` 中的所有相关设置，包括：
        *   **情绪展现：** 确保问题选项中情绪的表情符号、颜色和动画呈现严格按照 `final_presentation_config` 中的 `layer4_view_detail.emotion_presentation` 进行。
        *   **量表渲染：** 根据 `quiz_questions` 和 `quiz_question_options` 中的 `question_config` 和 `scoring_config` 动态渲染不同类型的量表（例如，滑块、选择点），并应用任何视觉覆盖（如量表点上的自定义表情符号）。
        *   **其他视图细节：** 应用 `layer4_view_detail` 中的 `wheel_config`、`card_config` 等来定制视图的各个方面。
2.  **`WheelTest.tsx` - 转向生产数据和个性化：**
    *   **移除模拟数据：** 将 `useMockEmotionData()` 替换为从实际服务（例如，通过 tRPC 查询 `quiz_questions` 和 `quiz_question_options` 来获取情感/量表数据）获取数据的逻辑。
    *   **集成个性化展现：** 确保 `WheelTest.tsx` 能够获取并应用用户的 `user_presentation_configs`（特别是 `layer4_view_detail.emotion_presentation` 中的表情符号映射、颜色和动画），以在滚轮上动态地展现个性化的表情符号。这将直接反映 `user_emoji_preferences.sql` 中的测试数据。

---

#### 四、 主页和测验启动：`src/pages/NewHome.tsx` 和 `src/pages/QuizLauncher.tsx` 的迁移重点

这些页面是用户体验的入口点，需要确保它们利用个性化数据提供定制化的内容。

**当前差距回顾：**

*   **`NewHome.tsx`：** 可能未能充分利用 `UserQuizPreferencesService` 和 `QuizConfigMergerService` 来根据用户配置的偏好动态过滤和呈现测验包。
*   **`QuizLauncher.tsx`：** 未明确说明如何进行 `generateSessionConfig` 的调用以及其输出如何被后续 `QuizSession.tsx` 页面使用。

**迁移步骤与具体行动：**

1.  **`NewHome.tsx` - 个性化测验包推荐：**
    *   **增强 `useNewHomeData`：** 修改 `useNewHomeData`，使其不仅按类别获取测验包，还要利用 `UserQuizPreferencesService` 中存储的用户偏好（例如，`layer0_dataset_presentation.preferred_pack_categories` 和 `auto_select_recommended`）以及可能通过 `QuizConfigMergerService` 获得的合并配置来动态地过滤、排序和推荐测验包。
    *   **UI 展现：** 调整 UI 以更突出地显示个性化推荐的测验包，并可能提供用户可以调整其推荐偏好的入口（指向 `QuizSettings.tsx`）。
2.  **`QuizLauncher.tsx` - 确保会话配置生成：**
    *   **强制调用 `generateSessionConfig`：** 在 `QuizLauncher.tsx` 中，确保在用户确认启动测验后，显式调用 `config.quiz.generateSessionConfig` tRPC 端点。
    *   **传递 `sessionId` 和 `final_presentation_config`：** 成功生成会话配置后，获取返回的 `sessionId` 和 `final_presentation_config`，并确保将 `sessionId` 传递给 `QuizSession.tsx`。理想情况下，`final_presentation_config` 会被存储在 `quiz_session_presentation_configs` 中，供 `QuizSession.tsx` 在需要时检索。

---

#### 五、 内容管理页面：`src/pages/QuizManagementPage.tsx` 的迁移重点

对于内容创建和管理，需要强大的在线 CRUD 支持。

**当前差距回顾：**

*   设计中没有明确详细说明用于 `createQuizPack`、`updateQuizPack`、`deleteQuizPack`、`createQuestion`、`updateQuestion`、`deleteQuestion` 或 `reorderQuestions` 的 tRPC 端点。

**迁移步骤与具体行动：**

1.  **实现全面的 tRPC CRUD 端点：**
    *   **后端开发：** 在 `server/lib/routers/` 目录中，为 `QuizPackService` 和 `QuizQuestionService` 实现所有必要的 tRPC 端点，以支持测验包和问题的在线创建、读取、更新、删除以及问题重新排序。这些端点应能处理包含 JSON 结构（如 `quiz_logic_config`、`question_config`）的数据。
    *   **前端集成：** `QuizManagementPage.tsx` 中的 `useQuizManagement()` Hook 需要利用这些新的 tRPC 端点来执行其数据操作。

---

#### 六、 `server/lib` 与 `docs/quiz/user-personalization-guide.md` 的协调

在整个页面迁移过程中，需要确保前端（`src/pages`）与后端（`server/lib`）以及设计文档（`docs/quiz/user-personalization-guide.md`）之间保持高度一致性。

**具体行动：**

1.  **`server/lib/routers/` 验证：** 检查现有的 tRPC 路由（例如 `router.ts` 或 `routers` 目录下的其他路由），确保它们已经实现了 `config.quiz`、`config.emoji` 等路由中描述的端点，并支持对 `user_presentation_configs`、`pack_presentation_overrides`、`quiz_packs` 和 `quiz_questions` 中 JSON 字段的读写操作。如果缺失，则需要补充实现。
2.  **`server/lib/services/` 增强：** 确保后端服务（例如 `QuizConfigMergerService`、`UserQuizPreferencesService`、`QuizPackService`）能够正确处理、合并和验证这些 JSON 配置数据。
3.  **遵循个性化指南：** 严格按照 `docs/quiz/user-personalization-guide.md` 中描述的 6 层架构和配置合并优先级来实现 UI 和后端逻辑，确保所有个性化设置能正确应用。

---

**总体优先级：**

1.  **高优先级：** `QuizSettings.tsx` (全面配置管理) 和 `EmojiSetManager.tsx` (细粒度表情符号映射)。这两个页面直接影响用户个性化设置的输入。
2.  **次高优先级：** `QuizSession.tsx` 和 `QuizLauncher.tsx` (确保 `final_presentation_config` 的正确消费)。这些页面直接影响用户个性化体验的输出。
3.  **中优先级：** `NewHome.tsx` (个性化推荐) 和 `QuizManagementPage.tsx` (在线内容管理)。这些页面提升用户体验和管理效率。
4.  **低优先级：** `WheelTest.tsx` (从模拟数据转向生产数据和个性化)。

通过遵循此迁移计划，可以系统地解决页面在量表和配置方面存在的滞后问题，确保系统完全符合其新的架构设计和用户个性化功能。 