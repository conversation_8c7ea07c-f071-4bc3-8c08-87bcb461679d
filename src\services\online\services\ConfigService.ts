/**
 * 在线配置服务
 * 处理Quiz配置、表情符号映射等配置相关的在线操作
 */

import { trpc } from '@/lib/trpc';
import type { QuizSessionConfig, QuizPresentationConfig } from '@/types/schema/index';

export class ConfigService {
  private trpcClient: typeof trpc;

  constructor(trpcClient: typeof trpc) {
    this.trpcClient = trpcClient;
  }

  /**
   * 生成Quiz会话配置
   * 调用服务器端的配置合并逻辑
   */
  async generateSessionConfig(
    userId: string,
    packId: string,
    sessionId: string
  ): Promise<{ success: boolean; data?: QuizSessionConfig; error?: string }> {
    try {
      console.log('[ConfigService] Generating session config:', { userId, packId, sessionId });

      // 调用tRPC端点生成会话配置
      const result = await this.trpcClient.config.quiz.generateSessionConfig.mutate({
        userId,
        packId,
        sessionId
      });

      if (result.success) {
        console.log('[ConfigService] Session config generated successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[ConfigService] Failed to generate session config:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ConfigService] Error generating session config:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 更新用户表情符号映射
   */
  async updateUserEmojiMapping(
    userId: string,
    emotionId: string,
    emojiMapping: { primary: string; alternatives: string[] },
    color?: string,
    animation?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('[ConfigService] Updating user emoji mapping:', { 
        userId, 
        emotionId, 
        emojiMapping 
      });

      const result = await this.trpcClient.config.emoji.updateUserEmojiMapping.mutate({
        userId,
        emotionId,
        emojiMapping,
        color,
        animation
      });

      if (result.success) {
        console.log('[ConfigService] Emoji mapping updated successfully');
        return { success: true };
      } else {
        console.warn('[ConfigService] Failed to update emoji mapping:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ConfigService] Error updating emoji mapping:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 获取用户的Quiz配置偏好
   */
  async getUserQuizPreferences(
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.log('[ConfigService] Getting user quiz preferences:', userId);

      const result = await this.trpcClient.config.quiz.getUserPreferences.query({
        userId
      });

      if (result.success) {
        console.log('[ConfigService] User quiz preferences retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[ConfigService] Failed to get user quiz preferences:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ConfigService] Error getting user quiz preferences:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 更新用户的Quiz配置偏好
   */
  async updateUserQuizPreferences(
    userId: string,
    preferences: Partial<QuizPresentationConfig>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('[ConfigService] Updating user quiz preferences:', { userId, preferences });

      const result = await this.trpcClient.config.quiz.updateUserPreferences.mutate({
        userId,
        preferences
      });

      if (result.success) {
        console.log('[ConfigService] User quiz preferences updated successfully');
        return { success: true };
      } else {
        console.warn('[ConfigService] Failed to update user quiz preferences:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ConfigService] Error updating user quiz preferences:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 同步用户配置到云端
   */
  async syncUserConfigToCloud(
    userId: string,
    configData: any
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('[ConfigService] Syncing user config to cloud:', userId);

      const result = await this.trpcClient.config.sync.uploadUserConfig.mutate({
        userId,
        configData
      });

      if (result.success) {
        console.log('[ConfigService] User config synced to cloud successfully');
        return { success: true };
      } else {
        console.warn('[ConfigService] Failed to sync user config to cloud:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ConfigService] Error syncing user config to cloud:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 从云端下载用户配置
   */
  async downloadUserConfigFromCloud(
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.log('[ConfigService] Downloading user config from cloud:', userId);

      const result = await this.trpcClient.config.sync.downloadUserConfig.query({
        userId
      });

      if (result.success) {
        console.log('[ConfigService] User config downloaded from cloud successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[ConfigService] Failed to download user config from cloud:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ConfigService] Error downloading user config from cloud:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 重置用户配置到默认值
   */
  async resetUserConfigToDefault(
    userId: string,
    configType: 'global' | 'quiz' | 'emoji' | 'all' = 'all'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('[ConfigService] Resetting user config to default:', { userId, configType });

      const result = await this.trpcClient.config.reset.resetToDefault.mutate({
        userId,
        configType
      });

      if (result.success) {
        console.log('[ConfigService] User config reset to default successfully');
        return { success: true };
      } else {
        console.warn('[ConfigService] Failed to reset user config to default:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ConfigService] Error resetting user config to default:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 获取系统默认配置
   */
  async getSystemDefaultConfig(
    configType: 'global' | 'quiz' | 'emoji'
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.log('[ConfigService] Getting system default config:', configType);

      const result = await this.trpcClient.config.defaults.getSystemDefaults.query({
        configType
      });

      if (result.success) {
        console.log('[ConfigService] System default config retrieved successfully');
        return { success: true, data: result.data };
      } else {
        console.warn('[ConfigService] Failed to get system default config:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ConfigService] Error getting system default config:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * 验证配置数据的有效性
   */
  async validateConfig(
    configData: any,
    configType: 'global' | 'quiz' | 'emoji'
  ): Promise<{ success: boolean; isValid?: boolean; errors?: string[]; error?: string }> {
    try {
      console.log('[ConfigService] Validating config:', { configType });

      const result = await this.trpcClient.config.validation.validateConfig.mutate({
        configData,
        configType
      });

      if (result.success) {
        console.log('[ConfigService] Config validation completed');
        return { 
          success: true, 
          isValid: result.data.isValid, 
          errors: result.data.errors 
        };
      } else {
        console.warn('[ConfigService] Failed to validate config:', result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('[ConfigService] Error validating config:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
}

export default ConfigService;
